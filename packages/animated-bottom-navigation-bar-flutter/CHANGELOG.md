## [1.3.3] - 2024-01-31
* Fix animation controllers memory leak by [Clon1998](https://github.com/Clon1998)

## [1.3.2] - 2024-01-18
* Add `blurFilter` parameter

## [1.3.1] - 2024-01-17
* Fix [#65](https://github.com/LanarsInc/animated-bottom-navigation-bar-flutter/issues/65)

## [1.3.0] - 2023-09-22
* Add `scaleFactor` parameter by [Oubi256](https://github.com/Oubi256) 
* Update README.md

## [1.2.1] - 2023-09-13
* Bump sdk version to `>=2.12.0 <4.0.0`
* Update README.md

## [1.2.0] - 2023-03-09
* Fix [#46](https://github.com/LanarsInc/animated-bottom-navigation-bar-flutter/issues/46)
* Update example

## [1.1.0+1] - 2022-12-27
* Update README.md

## [1.1.0] - 2022-08-18
* Add `elevation` parameter
* Remove default value for `shadow`

## [1.0.1] - 2022-07-04
* Fix [#40](https://github.com/vizhan-lanars/animated-bottom-navigation-bar-flutter/issues/40)

## [1.0.0] - 2022-06-17.
* **Breaking change**. `SafeArea` params grouped to `SafeAreaValues` object.
* **Breaking change**. Remove `elevation` parameter in favour of `shadow` parameter.
* Add shadow customization by [Prn-Ice](https://github.com/Prn-Ice)
* Add hide on scroll animation by [Prn-Ice](https://github.com/Prn-Ice)
* Add border customization
* Add `backgroundGradient` parameter
* Fix [#34](https://github.com/vizhan-lanars/animated-bottom-navigation-bar-flutter/issues/34)
* Add `blurEffect` parameter

## [0.3.3] - 2022-05-18.
* Add SafeArea setting by [DenchikBY](https://github.com/DenchikBY)
* Fix floating-point comparison by [franciscrispin](https://github.com/franciscrispin)

## [0.3.2] - 2021-03-22.
* Fix [#16](https://github.com/LanarsInc/animated-bottom-navigation-bar-flutter/issues/16)
* Update example

## [0.3.1+1] - 2021-03-10.
* Clean up code

## [0.3.1] - 2021-03-09.
* Fix [#2](https://github.com/LanarsInc/animated-bottom-navigation-bar-flutter/issues/2)

## [0.3.0] - 2021-03-09.
* Migrate to null safety [#15](https://github.com/LanarsInc/animated-bottom-navigation-bar-flutter/issues/15)

## [0.2.1] - 2020-12-27.
* Resolve `List` warning

## [0.2.0] - 2020-11-30.

* Enhancement [#4](https://github.com/LanarsInc/animated-bottom-navigation-bar-flutter/issues/4)
* Enhancement [#5](https://github.com/LanarsInc/animated-bottom-navigation-bar-flutter/issues/5)
* Add `NotchSmoothness.sharpEdge` type

## [0.1.3] - 2020-11-26.

* Fix [#1](https://github.com/LanarsInc/animated-bottom-navigation-bar-flutter/issues/1) by [LuizFilipeMedeira](https://github.com/LuizFilipeMedeira)

## [0.1.2+2] - 2020-04-24.

* Update package README.md

## [0.1.2+1] - 2020-04-23.

* Add example source files

## [0.1.2] - 2020-04-23.

* Update example README.md

## [0.1.1] - 2020-04-23.

* Update asset links

## [0.1.0] - 2020-04-23.

* Format files

## [0.0.1] - 2020-04-23.

* Initial release of `animated_bottom_navigation_bar`