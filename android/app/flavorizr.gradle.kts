import com.android.build.gradle.AppExtension

val android = project.extensions.getByType(AppExtension::class.java)

android.apply {
    flavorDimensions("flavor-type")

    productFlavors {
        create("dev") {
            dimension = "flavor-type"
            applicationId = "com.shinex.nexgo"
            resValue(type = "string", name = "app_name", value = "(Dev) Base App")
        }
        create("prod") {
            dimension = "flavor-type"
            applicationId = "com.shinex.nexgo"
            resValue(type = "string", name = "app_name", value = "Base App")
        }
    }
}