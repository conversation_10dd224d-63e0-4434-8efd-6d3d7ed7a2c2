name: base_app
description: "A base Flutter project."

publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.8

  flutter_bloc: ^9.1.1
  injectable: ^2.5.1
  get_it: ^8.2.0
  dio: ^5.9.0
  fpdart: ^1.1.1
  shared_preferences: ^2.5.3
  go_router: ^16.2.1
  logger: ^2.6.1
  json_annotation: ^4.9.0
  equatable: ^2.0.7
  freezed: ^3.1.0
  freezed_annotation: ^3.1.0
  retrofit: ^4.6.0
  pretty_dio_logger: ^1.4.0
  lottie: ^3.3.1
  flutter_flavorizr: ^2.4.1
  loading_animation_widget: ^1.3.0
  flutter_screenutil: ^5.9.3
  flutter_localizations:
    sdk: flutter
  device_info_plus: ^11.5.0
  package_info_plus: ^8.3.0
  get: ^4.7.2
  internet_connection_checker: ^3.0.1
  firebase_messaging: ^15.1.6
  firebase_auth: ^5.5.3
  firebase_core: ^3.13.0
  pinput: ^5.0.2
  flutter_localization: ^0.3.3
  connectivity_plus: ^6.0.5
  rxdart: ^0.28.0
  animated_bottom_navigation_bar:
    path: ./packages/animated-bottom-navigation-bar-flutter
  mapbox_maps_flutter: ^2.7.0
  auto_size_text: ^3.0.0
  permission_handler: ^12.0.1
  intl: ^0.19.0
  flutter_html: ^3.0.0
  url_launcher: ^6.3.2
  just_the_tooltip: ^0.0.12
  flutter_svg: ^2.2.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^5.0.0

  # Code Generation
  build_runner: ^2.4.13
  flutter_gen_runner: ^5.4.0
  injectable_generator: ^2.6.1
  json_serializable: ^6.9.0
  retrofit_generator: ^9.7.0

flutter_gen:
  output: lib/gen/
  integrations:
    image: true
    flutter_svg: true
    lottie: true

flutter:
  uses-material-design: true
  assets:
    - assets/lotties/
    - assets/icons/
    - assets/fonts/
    - assets/svgs/


# dart run flutter_flavorizr -p android:buildGradle,android:androidManifest,ios:podfile,ios:xcconfig,ios:buildTargets
flavorizr:
  flavors:
    dev:
      app:
        name: "(Dev) Base App"

      android:
        applicationId: "com.shinex.nexgo"
      ios:
        bundleId: "com.shinex.nexgo"

    prod:
      app:
        name: "Base App"

      android:
        applicationId: "com.shinex.nexgo"

      ios:
        bundleId: "com.shinex.nexgo"