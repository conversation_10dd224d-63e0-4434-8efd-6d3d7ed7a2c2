// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:dio/dio.dart' as _i361;
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;

import '../../features/home/<USER>/home_cubit.dart' as _i1032;
import '../../features/home/<USER>/datasource/home_remote.dart' as _i27;
import '../../features/home/<USER>/repositories/home_repository.dart' as _i0;
import '../../features/login/cubit/auth_cubit.dart' as _i862;
import '../../features/login/cubit/login_cubit.dart' as _i209;
import '../../features/login/data/datasources/login_remote.dart' as _i806;
import '../../features/login/domain/repositories/login_repository.dart'
    as _i902;
import '../../features/main/cubit/bottom_bar_cubit.dart' as _i1006;
import '../../features/otp/cubit/otp_cubit.dart' as _i420;
import '../../features/otp/data/datasource/otp_remote.dart' as _i686;
import '../../features/otp/domain/repositories/otp_repository.dart' as _i573;
import '../../features/signup/bloc/signup_bloc.dart' as _i381;
import '../../features/signup/data/datasource/signup_remote.dart' as _i375;
import '../../features/signup/domain/repositories/signup_repository.dart'
    as _i631;
import '../app/cubit/app_cubit.dart' as _i356;
import '../app/data/datasources/app_remote.dart' as _i312;
import '../app/domain/repositories/app_repository.dart' as _i33;
import '../local_storage/i_local_storage_service.dart' as _i316;
import '../local_storage/local_storage_service.dart' as _i71;
import '../network/dio_client.dart' as _i667;
import '../services/internet_connectivity_service.dart' as _i854;
import 'injection_module.dart' as _i212;

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  _i174.GetIt init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    final injectionModule = _$InjectionModule();
    gh.factory<_i420.OtpCubit>(() => _i420.OtpCubit());
    gh.factory<_i381.SignupBloc>(() => _i381.SignupBloc());
    gh.factory<_i209.LoginCubit>(() => _i209.LoginCubit());
    gh.lazySingleton<_i356.AppCubit>(() => _i356.AppCubit());
    gh.lazySingleton<_i667.DioClient>(() => _i667.DioClient());
    gh.lazySingleton<_i854.InternetConnectivityService>(
        () => _i854.InternetConnectivityService());
    gh.lazySingleton<_i1032.HomeCubit>(() => _i1032.HomeCubit());
    gh.lazySingleton<_i1006.BottomBarCubit>(() => _i1006.BottomBarCubit());
    gh.lazySingleton<_i862.AuthCubit>(() => _i862.AuthCubit());
    gh.lazySingleton<_i316.ILocalStorageService>(
        () => _i71.LocalStorageService());
    gh.lazySingleton<_i361.Dio>(
        () => injectionModule.dio(gh<_i667.DioClient>()));
    gh.lazySingleton<_i312.AppRemote>(() => _i312.AppRemote(gh<_i361.Dio>()));
    gh.lazySingleton<_i27.HomeRemote>(() => _i27.HomeRemote(gh<_i361.Dio>()));
    gh.lazySingleton<_i686.OtpRemote>(() => _i686.OtpRemote(gh<_i361.Dio>()));
    gh.lazySingleton<_i375.SignupRemote>(
        () => _i375.SignupRemote(gh<_i361.Dio>()));
    gh.lazySingleton<_i806.LoginRemote>(
        () => _i806.LoginRemote(gh<_i361.Dio>()));
    gh.lazySingleton<_i902.LoginRepository>(
        () => _i902.LoginRepository(gh<_i806.LoginRemote>()));
    gh.lazySingleton<_i33.AppRepository>(
        () => _i33.AppRepository(gh<_i312.AppRemote>()));
    gh.lazySingleton<_i631.SignupRepository>(
        () => _i631.SignupRepository(gh<_i375.SignupRemote>()));
    gh.lazySingleton<_i573.OtpRepository>(
        () => _i573.OtpRepository(gh<_i686.OtpRemote>()));
    gh.lazySingleton<_i0.HomeRepository>(
        () => _i0.HomeRepository(gh<_i27.HomeRemote>()));
    return this;
  }
}

class _$InjectionModule extends _i212.InjectionModule {}
