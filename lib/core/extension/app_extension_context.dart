import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

extension ContextExtension on BuildContext {
  ThemeData get theme => Theme.of(this);
  TextTheme get textTheme => theme.textTheme;
  ColorScheme get colorScheme => theme.colorScheme;
  MediaQueryData get mediaQuery => MediaQuery.of(this);
  Size get screenSize => mediaQuery.size;
  EdgeInsets get padding => mediaQuery.padding;
  EdgeInsets get viewInsets => mediaQuery.viewInsets;
  double get height => screenSize.height;
  double get width => screenSize.width;
  double dynamicHeight(double val) => height * val;
  double dynamicWidth(double val) => width * val;
  double get lowValue => height * 0.01;
  double get normalValue => height * 0.02;
  double get mediumValue => height * 0.04;
  double get highValue => height * 0.1;
  double get valuePaddingSmall => 4;
  double get valuePaddingNormal => 8;
  double get valuePaddingMedium => 12;
  double get valuePaddingHigh => 16;
  double get bottomNavHeight => 52;
  double get quickActionBtSize => 50;
  double get appBarHeight => AppBar().preferredSize.height;

  double get statusBarHeight => MediaQuery.of(this).padding.top;
  double get bottomBarHeight => MediaQuery.of(this).padding.bottom;
  double get keyboardHeight => MediaQuery.of(this).viewInsets.bottom;

  Widget get verticalSpaceSmall => 4.verticalSpace;
  Widget get verticalSpaceNormal => 8.verticalSpace;
  Widget get verticalSpaceMedium => 12.verticalSpace;
  Widget get verticalSpaceHigh => 16.verticalSpace;
  Widget get verticalSpaceVeryHigh => 24.verticalSpace;
  Widget get verticalSpaceLarge => 32.verticalSpace;

  Widget get horizontalSpaceSmall => 4.horizontalSpace;
  Widget get horizontalSpaceNormal => 8.horizontalSpace;
  Widget get horizontalSpaceMedium => 12.horizontalSpace;
  Widget get horizontalSpaceHigh => 16.horizontalSpace;
  Widget get horizontalSpaceLarge => 32.horizontalSpace;

  Widget verticalSpaceCustom(double value) => value.verticalSpace;

  BorderRadiusGeometry get borderRadiusMedium => BorderRadius.circular(12.r);
  EdgeInsets get paddingBody =>
      EdgeInsets.symmetric(horizontal: valuePaddingHigh);
  EdgeInsets get pdNormalHorizontal =>
      EdgeInsets.symmetric(horizontal: valuePaddingNormal);
  EdgeInsets get pdHighHorizontal =>
      EdgeInsets.symmetric(horizontal: valuePaddingHigh);
  EdgeInsets get pdHighVertical =>
      EdgeInsets.symmetric(vertical: valuePaddingHigh);
  EdgeInsets get paddingSmall => EdgeInsets.all(valuePaddingSmall);
  EdgeInsets get paddingNormal => EdgeInsets.all(valuePaddingNormal);
  EdgeInsets get paddingMedium => EdgeInsets.all(valuePaddingMedium);
  EdgeInsets get paddingHigh => EdgeInsets.all(valuePaddingHigh);
}
