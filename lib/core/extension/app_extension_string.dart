import 'package:base_app/core/translation/en.dart';
import 'package:base_app/core/translation/vi.dart';
import 'package:base_app/core/utils/language_util.dart';

extension StringTranslation on String {
  String get tr {
    final translations = {
      'en': enLanguage,
      'vi': viLanguage,
    };

    final langCode = LanguageUtil.getLocaleFromDevice().languageCode;
    return translations[langCode]?[this] ?? this;
  }

  String trParams( Map<String, String> params) {
    String text = tr;
    params.forEach((key, value) {
      text = text.replaceAll('@$key', value);
    });
    return text;
  }
}
