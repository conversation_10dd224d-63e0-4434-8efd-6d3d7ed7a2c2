import 'package:base_app/core/extension/extension_time.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

extension DateTimeCustom on DateTime {
  DateTime onlyYear() => DateTime(year);
  DateTime onlyMonthYear() => DateTime(year, month);
  DateTime onlyDayMonthYear() => DateTime(year, month, day);
  String getTitleMonthYear() {
    return DateFormat('MMMM yyyy').format(this);
  }

  num getTimestamp() {
    return toLocal().millisecondsSinceEpoch ~/ 1000;
  }

  String getYearMonthDay() {
    return DateFormat('yyyy-MM-dd').format(this);
  }

  String getHm() {
    return DateFormat('dd-MM').format(this);
  }

  String getYearMonthDayHour() {
    return DateFormat('yyyy-MM-dd HH:mm').format(this);
  }

  String getYMDayHour() {
    return DateFormat('dd/MM/yyyy HH:mm').format(this);
  }

  String getYMDH() {
    return DateFormat('dd/MM/yyyy-HH:mm').format(this);
  }

  String getHDMY() {
    return DateFormat('HH:mm - dd/MM/yyyy').format(this);
  }

  String getTimeDay() {
    return DateFormat('HH:mm').format(this);
  }

  String getDayTime() {
    return DateFormat('dd/MM HH:mm').format(this);
  }

  String getDayMonthYear() {
    return DateFormat('dd/MM/yyyy').format(this);
  }

  String getWeekDayMonth() {
    return DateFormat('EEEE dd/MM/yyyy', "vi").format(this);
  }

  String getMonthDayWeek() {
    return DateFormat('EEEE MM.dd', "vi").format(this);
  }

  String getDateTimeFormat(String format) {
    return DateFormat(format, "vi").format(this);
  }

  String getTime() {
    return DateFormat.yMMMMd().format(this);
  }

  String getTimeOffDay() {
    final TimeOfDay timeOfDay = TimeOfDay(hour: hour, minute: minute);
    return timeOfDay.getString();
  }

  String getTimePayment() {
    final TimeOfDay timeOfDay = TimeOfDay(
      hour: hour,
      minute: minute,
    );
    String type =
        timeOfDay.period.toString().split('.')[1] == 'am' ? '오전' : '오후';

    return '$type${timeOfDay.hour}:${timeOfDay.minute.toString().padLeft(2, '0')}';
  }

  int daysInMonth() {
    if (month > 12) {
      return 1;
    }
    final List<int> monthLength = List.filled(12, 0);
    monthLength[0] = 31;
    monthLength[2] = 31;
    monthLength[4] = 31;
    monthLength[6] = 31;
    monthLength[7] = 31;
    monthLength[9] = 31;
    monthLength[11] = 31;
    monthLength[3] = 30;
    monthLength[8] = 30;
    monthLength[5] = 30;
    monthLength[10] = 30;

    if (leapYear(year) == true) {
      monthLength[1] = 29;
    } else {
      monthLength[1] = 28;
    }

    return monthLength[month - 1];
  }

  bool leapYear(int year) {
    bool leapYear = false;
    final bool leap = (year % 100 == 0) && (year % 400 != 0);
    if (leap == true) {
      leapYear = false;
    } else if (year % 4 == 0) {
      leapYear = true;
    }

    return leapYear;
  }

  String getDateTQLFormat() {
    return '$year/$month/$day';
  }

  String getTimeAgo() {
    final dateTimeNow = DateTime.now();
    final valueDays = dateTimeNow.difference(this).inDays;
    final valueHours = dateTimeNow.difference(this).inHours;
    final valueMinutes = dateTimeNow.difference(this).inMinutes;
    final valueSeconds = dateTimeNow.difference(this).inSeconds;

    if (valueDays > 10) {
      final result = getYearMonthDayHour();
      return result;
    }
    if (valueDays > 0) {
      final result = "$valueDays ngày trước";
      return result;
    }

    if (valueHours > 0) {
      final result = "$valueHours giờ trước";
      return result;
    }
    if (valueMinutes > 0) {
      final result = "$valueMinutes phút trước";
      return result;
    }
    if (valueSeconds > 0) {
      final result = "$valueSeconds giây trước";
      return result;
    }
    return "Bây giờ";
  }
}
