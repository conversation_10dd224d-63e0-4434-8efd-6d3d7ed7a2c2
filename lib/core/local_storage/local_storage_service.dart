import 'dart:convert';

import 'package:base_app/core/constants/storage_constants.dart';
import 'package:base_app/features/home/<USER>/models/place_reponse.dart';
import 'package:base_app/features/login/data/models/user_model.dart';
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'i_local_storage_service.dart';
import 'local_storage.dart';

@LazySingleton(as: ILocalStorageService)
class LocalStorageService extends ILocalStorageService {
  final SharedPreferences _preferences = LocalStorage.instant.preferences;

  @override
  Future<bool> hasAuthenticated() async {
    String accessToken = await getAccessToken();
    return accessToken.isNotEmpty;
  }

  @override
  Future setAccessToken(String accessToken) async {
    await _preferences.setString(PrefCons.PREF_ACCESS_TOKEN, accessToken);
  }

  @override
  Future<String> getAccessToken() async {
    return _preferences.getString(PrefCons.PREF_ACCESS_TOKEN) ?? '';
  }

  @override
  Future clearAll() async {
    await _preferences.remove(PrefCons.PREF_ACCESS_TOKEN);
    bool isRememberPhone = await getRememberPhone();
    String phoneRemember = await getPhoneNumerRemember();
    await _preferences.clear();
    await setRememberPhone(isRememberPhone);
    await setPhoneNumerRemember(phoneRemember);
    return true;
  }

  @override
  Future<String> getRefreshToken() async {
    return _preferences.getString(PrefCons.PREF_REF_TOKEN) ?? '';
  }

  @override
  Future setRefreshToken(String refreshToken) async {
    await _preferences.setString(PrefCons.PREF_REF_TOKEN, refreshToken);
  }

  @override
  Future saveCredentials(String accessToken, UserModel user) async {
    await Future.wait([
      setAccessToken(accessToken),
      setLoggedUser(user),
    ]);
  }

  @override
  Future setLoggedUser(UserModel user) async {
    await _preferences.setString(
        PrefCons.PREF_LOGGED_ACCOUNT, jsonEncode(user));
  }

  @override
  Future setFCMToken(String fcmToken) async {
    await _preferences.setString(PrefCons.PREF_FCM_TOKEN, fcmToken);
  }

  @override
  Future<String> getFCMToken() async {
    return _preferences.getString(PrefCons.PREF_FCM_TOKEN) ?? '';
  }

  @override
  Future setUserPhone(String phone) async {
    await _preferences.setString(PrefCons.PREF_USER_PHONE, phone);
  }

  @override
  Future<String> getUserPhone() async {
    return _preferences.getString(PrefCons.PREF_USER_PHONE) ?? '';
  }

  @override
  Future<bool> setRememberPhone(bool value) async {
    return _preferences.setBool(PrefCons.PREF_REMEMBER_PHONE, value);
  }

  @override
  Future<bool> getRememberPhone() async {
    return _preferences.getBool(PrefCons.PREF_REMEMBER_PHONE) ?? false;
  }

  @override
  Future setPhoneNumerRemember(String phone) async {
    await _preferences.setString(PrefCons.PREF_PHONE_REMEMBER, phone);
  }

  @override
  Future<String> getPhoneNumerRemember() async {
    return _preferences.getString(PrefCons.PREF_PHONE_REMEMBER) ?? '';
  }

  @override
  Future removeCredentials() async {
    return Future.wait([
      _preferences.remove(PrefCons.PREF_ACCESS_TOKEN),
      _preferences.remove(PrefCons.PREF_LOGGED_ACCOUNT),
    ]);
  }

  @override
  Future setMapBoxKeyDecoded(String key) async {
    await _preferences.setString(PrefCons.PREF_MAP_BOX_KEY_DECODED, key);
  }

  @override
  Future<String?> getMapBoxKeyDecoded() async {
    return _preferences.getString(PrefCons.PREF_MAP_BOX_KEY_DECODED);
  }

  @override
  Future setMapBoxKey(String key) async {
    await _preferences.setString(PrefCons.PREF_MAP_BOX_KEY, key);
  }

  @override
  Future<String?> getMapBoxKey() async {
    return _preferences.getString(PrefCons.PREF_MAP_BOX_KEY);
  }

  @override
  Future savePlaceSelected(List<PlaceReponse> places) async {
    List<String> placesJsonList = places.map((place) {
      return jsonEncode(place.toJson());
    }).toList();

    return await _preferences.setStringList(
        PrefCons.PREF_PLACE_SELECTED, placesJsonList);
  }

  @override
  Future<List<PlaceReponse>> getPlaceSelected() async {
    final places = _preferences.getStringList(PrefCons.PREF_PLACE_SELECTED);
    if (places == null) {
      return [];
    }
    return places.map((placeJson) {
      Map<String, dynamic> placeMap = jsonDecode(placeJson);
      return PlaceReponse.fromJson(placeMap);
    }).toList();
  }
}
