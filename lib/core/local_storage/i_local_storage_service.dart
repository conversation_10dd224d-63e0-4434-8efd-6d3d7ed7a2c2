import 'package:base_app/features/home/<USER>/models/place_reponse.dart';
import 'package:base_app/features/login/data/models/user_model.dart';
import 'package:injectable/injectable.dart';

@factoryMethod
abstract class ILocalStorageService {
  Future<bool> hasAuthenticated();

  Future<String> getRefreshToken();

  Future setRefreshToken(String refreshToken);

  Future<String> getAccessToken();

  Future setAccessToken(String accessToken);

  Future clearAll();

  Future saveCredentials(String accessToken, UserModel user);

  Future setLoggedUser(UserModel user);

  Future setFCMToken(String fcmToken);

  Future<String> getFCMToken();

  Future setUserPhone(String phone);

  Future<String> getUserPhone();

  Future<bool> setRememberPhone(bool value);

  Future<bool> getRememberPhone();

  Future setPhoneNumerRemember(String phone);

  Future<String> getPhoneNumerRemember();

  Future removeCredentials();

  Future setMapBoxKeyDecoded(String key);

  Future<String?> getMapBoxKeyDecoded();
  
  Future setMapBoxKey(String key);

  Future<String?> getMapBoxKey();

  Future savePlaceSelected(List<PlaceReponse> places);

  Future<List<PlaceReponse>> getPlaceSelected();
}
