import 'dart:convert';

import 'package:flutter/services.dart';

class TokenDecoderUtil {
  static final List<int> _xorKey = [
    54,
    51,
    102,
    52,
    57,
    52,
    53,
    100,
    57,
    50,
    49,
    100,
    53,
    57,
    57,
    102,
    50,
    55,
    97,
    101,
    52,
    102,
    100,
    102,
    53,
    98,
    97,
    100,
    97,
    51,
    102,
    49,
    54,
    51,
    102,
    52,
    57,
    52,
    53,
    100,
    57,
    50,
    49,
    100,
    53,
    57,
    57,
    102,
    50,
    55,
    97,
    101,
    52,
    102,
    100,
    102,
    53,
    98,
    97,
    100,
    97,
    51,
    102,
    49,
    54,
    51,
    102,
    52,
    57,
    52,
    53,
    100,
    57,
    50,
    49,
    100,
    53,
    57,
    57,
    102,
    50,
    55,
    97,
    101,
    52,
    102,
    100,
    102,
    53
  ];

  static String decode(String encodedToken) {
    try {
      Uint8List encodedBytes = base64.decode(encodedToken);
      List<int> decodedBytes = [];

      for (int i = 0; i < encodedBytes.length; i++) {
        int keyByte = _xorKey[i % _xorKey.length];
        decodedBytes.add(encodedBytes[i] ^ keyByte);
      }

      return utf8.decode(decodedBytes);
    } catch (e) {
      throw Exception('Failed to decode token: $e');
    }
  }

  static bool isValid(String token) {
    return token.startsWith('sk.') || token.startsWith('pk.');
  }

  /// Lấy loại token (secret key hoặc public key)
  static String getTokenType(String token) {
    if (token.startsWith('sk.')) return 'Secret Key';
    if (token.startsWith('pk.')) return 'Public Key';
    return 'Unknown';
  }

  static String encode(String plainToken) {
    try {
      List<int> plainBytes = utf8.encode(plainToken);
      List<int> encodedBytes = [];

      for (int i = 0; i < plainBytes.length; i++) {
        int keyByte = _xorKey[i % _xorKey.length];
        encodedBytes.add(plainBytes[i] ^ keyByte);
      }

      return base64.encode(encodedBytes);
    } catch (e) {
      throw Exception('Failed to encode token: $e');
    }
  }
}
