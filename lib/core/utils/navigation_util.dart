import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class NavigationUtil {
  static CustomTransitionPage<void> withFade({
    required Widget page,
    LocalKey? key,
  }) {
    return CustomTransitionPage<void>(
      key: key,
      child: page,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(
          opacity: animation,
          child: child,
        );
      },
      transitionDuration: const Duration(milliseconds: 300),
    );
  }
}
