import 'package:base_app/core/extension/app_extension_context.dart';
import 'package:base_app/core/theme/app_colors.dart';
import 'package:base_app/core/theme/app_text_styles.dart';
import 'package:base_app/core/translation/app_string.dart';
import 'package:base_app/gen/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lottie/lottie.dart';

class DialogUtil {
  static Future<void> showNoInternetDialog(BuildContext context) async {
    try {
      return showDialog(
          context: context,
          barrierDismissible: false,
          builder: (dialogContext) {
            return PopScope(
              canPop: false,
              child: AlertDialog(
                backgroundColor: AppColors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16.r),
                ),
                title: Column(
                  children: [
                    context.verticalSpaceHigh,
                    Lottie.asset(
                      Assets.lotties.noConnection.path,
                      width: context.width * 0.5,
                      height: context.height * 0.2,
                      fit: BoxFit.cover,
                    ),
                    Text(
                      AppString.noInternetTitle,
                      style: AppTextStyle.bold(18.sp),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
                content: Text(
                  AppString.noInternetContent,
                  textAlign: TextAlign.center,
                  style: AppTextStyle.regular(14.sp),
                ),
              ),
            );
          },
        );
    } catch (e) {
      debugPrint('Error showing no internet dialog: $e');
    }
  }
}
