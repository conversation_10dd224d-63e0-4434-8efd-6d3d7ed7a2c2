import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class SafeNavigationUtil {
  static void pop(BuildContext context) {
    try {
      if (context.mounted && Navigator.canPop(context)) {
        context.pop();
      }
    } catch (e) {
      debugPrint('Error popping context: $e');
    }
  }

  static void popDialog(
    BuildContext context, {
    bool rootNavigator = false,
  }) {
    try {
      if (context.mounted) {
        Navigator.of(context, rootNavigator: rootNavigator).pop();
      }
    } catch (e) {
      debugPrint('Error closing dialog: $e');
    }
  }

  static void push(BuildContext context, String route, {Object? extra}) {
    try {
      if (context.mounted) {
        context.push(route, extra: extra);
      }
    } catch (e) {
      debugPrint('Error pushing route: $e');
    }
  }

  static void go(BuildContext context, String route, {Object? extra}) {
    try {
      if (context.mounted) {
        context.go(route, extra: extra);
      }
    } catch (e) {
      debugPrint('Error going to route: $e');
    }
  }
}
