class StringUtils {
  StringUtils._();
  static bool isNullOrEmpty(String? value) =>
      value == null || value.trim().isEmpty;

  static bool isNotNullOrEmpty(String? value) => !isNullOrEmpty(value);
  static String? returnNullIfEmpty(String str) {
    return str.isEmpty ? null : str;
  }
}

extension StringExt on String {
  double toDouble() {
    if (isEmpty) {
      return 0;
    } else {
      return double.tryParse(replaceAll(".", ",").replaceAll(",", ".")) ?? 0;
    }
  }

  int toInt() {
    if (isEmpty) {
      return 0;
    } else {
      return int.tryParse(replaceAll(".", ",").replaceAll(",", ".")) ?? 0;
    }
  }

  String get formatDecimal {
    String newStr = replaceAll('.', '').replaceAll(',', '.');
    return newStr;
  }

  String get replaceToComma {
    String newStr = replaceAll(' ', '').replaceAll('.', ',');
    return newStr;
  }

  String get breakWord {
    String breakWord = '';
    for (var element in runes) {
      breakWord += String.fromCharCode(element);
      breakWord += '\u200B';
    }
    return breakWord;
  }

  String get formatPhoneNumber {
    // Check if the phone number starts with '0'
    if (startsWith('0')) {
      // Replace the first '0' with '+84'
      return '+84${substring(1)}';
    }
    // If it doesn't start with '0', return the number as it is (you can handle this differently if needed)
    return this;
  }
}
