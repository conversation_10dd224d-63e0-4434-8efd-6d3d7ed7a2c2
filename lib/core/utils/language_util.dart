import 'package:base_app/core/constants/app_constants.dart';
import 'package:base_app/core/translation/en.dart';
import 'package:base_app/core/translation/vi.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class LanguageUtil extends Translations {
  static const fallbackLocaleEn = Locale('en', 'US');
  static const fallbackLocaleVi = Locale('vi', 'VN');


  static const supportLocale = AppConstants.supportedLocales;

  static Locale getLocaleFromDevice() {
    return _getLocaleFromLanguageCurrent();
  }

  static void changeLocale(String languageCode) {
    final locale = _getLocaleFromLanguageCurrent(languageCode: languageCode);
    Get.updateLocale(locale);
  }

  static Locale _getLocaleFromLanguageCurrent({String? languageCode}) {
    final lang = languageCode ?? Get.deviceLocale!.languageCode;
    for (var i in supportLocale) {
      if (lang == i.languageCode) {
        return i;
      }
    }
    return Get.locale!;
  }

  @override
  Map<String, Map<String, String>> get keys => {
        'en': enLanguage,
        'vi': viLanguage,
      };
}
