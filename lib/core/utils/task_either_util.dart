import 'package:base_app/core/error/app_failures.dart';
import 'package:base_app/core/utils/loading_overlay_util.dart';
import 'package:dio/dio.dart';
import 'package:fpdart/fpdart.dart';

typedef FutureEither<T> = Future<Either<AppFailures, T>>;

class TaskEitherUtils {
  TaskEitherUtils._();

  static FutureEither<T> fromFuture<T>(Future<T> future) async {
    try {
      final result = await future;
      return Right(result);
    } catch (error, stackTrace) {
      return Left(_handleError(error, stackTrace));
    }
  }

  static AppFailures _handleError(Object error, StackTrace stackTrace) {
    AppLoadingOverlay.hide();
    if (error is AppFailures) {
      return error;
    }

    if (error is DioException) {
      return _handleDioError(error);
    }

    final errorMessage = error.toString();

    if (errorMessage.contains('SocketException') ||
        errorMessage.contains('NetworkException') ||
        errorMessage.contains('No Internet')) {
      return NetworkFailure(
        message: 'Network connection failed',
      );
    }

    if (errorMessage.contains('TimeoutException')) {
      return NetworkFailure(
        message: 'Request timeout',
      );
    }

    return UnknownFailure(
      message: 'An unexpected error occurred: $errorMessage',
    );
  }

  static AppFailures _handleDioError(DioException dioError) {
    final responseSuccess = dioError.response?.data['message'];
    final data = responseSuccess;

    switch (dioError.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return NetworkFailure(
          message: 'Request timeout',
        );

      case DioExceptionType.connectionError:
        return NetworkFailure(
          message: 'Network connection failed',
        );

      case DioExceptionType.badResponse:
        return ServerFailure(
          message: data?[0]['msg'] ?? 'Server error occurred',
          statusCode: dioError.response?.statusCode,
        );

      case DioExceptionType.cancel:
        return NetworkFailure(
          message: 'Request was cancelled',
        );

      case DioExceptionType.unknown:
      default:
        return UnknownFailure(
          message: 'Network error: ${dioError.message}',
        );
    }
  }
}
