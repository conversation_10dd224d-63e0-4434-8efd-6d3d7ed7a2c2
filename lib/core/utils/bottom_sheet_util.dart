import 'package:base_app/core/extension/app_extension_context.dart';
import 'package:base_app/core/theme/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BottomSheetUtil {
  static Future<void> show({
    required BuildContext context,
    Widget? widgetCustom,
    bool isCheck = false,
    bool isKeyboard = false,
  }) {
    return showModalBottomSheet(
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      context: context,
      builder: (context) {
        return Container(
          padding: EdgeInsets.only(
            bottom: isKeyboard
                ? MediaQuery.of(context).viewInsets.bottom
                : context.bottomBarHeight + 16,
            top: context.valuePaddingHigh,
            right: context.valuePaddingHigh,
            left: context.valuePaddingHigh,
          ),
          margin: EdgeInsets.only(
            top: isCheck ? context.appBarHeight * 2 : 0,
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.r),
            color: AppColors.white,
          ),
          child: widgetCustom,
        );
      },
    );
  }
}
