enum FirebaseErrorCode {
  invalidPhone('invalid-phone-number'),
  tooManyRequests('too-many-requests'),
  webContextCancelled('web-context-cancelled'),
  invalidVer('invalid-verification-code'),
  sessionExpired('session-expired'),
  invalidVerId('invalid-verification-id'),
  networkRequestFailed('network-request-failed'),
  unknown('');

  final String code;
  const FirebaseErrorCode(this.code);

  static FirebaseErrorCode fromCode(String? code) {
    return FirebaseErrorCode.values.firstWhere(
      (e) => e.code == code,
      orElse: () => FirebaseErrorCode.unknown,
    );
  }
}
