import 'package:equatable/equatable.dart';

abstract class AppFailures extends Equatable {
  const AppFailures({
    this.message = 'Unknown Error',
    this.statusCode,
  });

  final String message;
  final int? statusCode;

  @override
  List<Object> get props => [message, statusCode ?? -1];
}

class ServerFailure extends AppFailures {
  const ServerFailure({super.message = 'Server Error', super.statusCode});
}

class NetworkFailure extends AppFailures {
  const NetworkFailure({super.message = 'Network Error'});
}

class UnknownFailure extends AppFailures {
  const UnknownFailure({super.message = 'Unknown Error'});
}
