import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:logger/logger.dart';

class AppLog {
  AppLog._();
  static final Logger logger = Logger(
    printer: PrettyPrinter(
      methodCount: 0,
      levelEmojis: {
        Level.debug: '🌈',
        Level.info: '🌸',
        Level.warning: '🟡',
        Level.error: '🔴',
      },
      colors: false,
      dateTimeFormat: (time) => '${time.hour}:${time.minute}:${time.second}',
    ),
  );

  static String cURLRepresentation(RequestOptions options) {
    final List<String> components = ['curl -i'];
    if (options.method.toUpperCase() != 'GET') {
      components.add('-X ${options.method}');
    }

    options.headers.forEach((k, v) {
      if (k != 'Cookie') {
        components.add('-H "$k: $v"');
      }
    });

    if (options.data != null) {
      if (options.data is FormData) {
        options.data = Map.fromEntries((options.data as FormData).fields);
      }

      final data = json.encode(options.data).replaceAll('"', '\\"');
      components.add('-d "$data"');
    }

    components.add('"${options.uri.toString()}"');

    return components.join(' \\\n\t');
  }
}
