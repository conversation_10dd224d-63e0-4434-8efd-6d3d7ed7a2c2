import 'package:base_app/core/constants/api_constants.dart';
import 'package:base_app/core/enums/app_enum.dart';

class FlavorConfig {
  static late String apiBaseUrl;
  static late String appEnv;

  static Future<void> initial(Environment env) async {
    switch (env) {
      case Environment.prod:
        apiBaseUrl = ApiConstants.baseUrlProd;
        appEnv = Environment.prod.value;
        break;

      default:
        apiBaseUrl = ApiConstants.baseUrlDev;
        appEnv = Environment.dev.value;
        break;
    }
  }
}
