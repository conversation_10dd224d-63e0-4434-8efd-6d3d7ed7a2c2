import 'dart:async';

import 'package:base_app/core/di/injection.dart';
import 'package:base_app/core/local_storage/i_local_storage_service.dart';
import 'package:base_app/features/login/cubit/auth_cubit.dart';
import 'package:flutter/material.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';

class AppHelper {
  AppHelper._();

  static final AppHelper instance = AppHelper._();

  void setAccessToken() async {
    final mapBoxKey = await getIt<ILocalStorageService>().getMapBoxKey() ?? '';
    if (mapBoxKey.isEmpty) {
      return;
    }
    AuthCubit authCubit = getIt<AuthCubit>();
    final status = authCubit.state.status;
    final isAuthenticated = status == AuthenticationStatus.authenticated;
    if (!isAuthenticated) {
      return;
    }
    debugPrint('setAccessToken => $mapBoxKey');
    MapboxOptions.setAccessToken(mapBoxKey);
  }

  void hideKeyboard(BuildContext context) {
    FocusScope.of(context).unfocus();
  }

  Future<void> delay(FutureOr<dynamic> Function()? computation) async {
    await Future.delayed(
      const Duration(milliseconds: 200),
      computation,
    );
  }

  // if (_debounce?.isActive ?? false) _debounce!.cancel();
  //               _debounce = Timer(const Duration(milliseconds: 700), () {
  //                 if (p0.isNotEmpty && p0.trim().isNotEmpty) {
  //                   homeCubit.getPlaces(place: p0);
  //                 }
  //               });
  Timer? _debounce;
  void debounce(VoidCallback callback, {int duration = 700}) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(Duration(milliseconds: duration), callback);
  }
}
