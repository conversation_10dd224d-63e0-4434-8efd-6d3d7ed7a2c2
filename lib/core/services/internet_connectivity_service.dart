import 'dart:async';

import 'package:base_app/core/extension/app_extension_context.dart';
import 'package:base_app/core/theme/app_colors.dart';
import 'package:base_app/core/theme/app_text_styles.dart';
import 'package:base_app/core/translation/app_string.dart';
import 'package:base_app/gen/assets.gen.dart';
import 'package:base_app/router/app_router.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:injectable/injectable.dart';
import 'package:lottie/lottie.dart';

@LazySingleton()
class InternetConnectivityService {
  final Connectivity _connectivity = Connectivity();
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  BuildContext? _dialogContext;

  void initialize() {
    Future.delayed(const Duration(milliseconds: 500), () {
      _checkInitialConnection();
    });

    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
      (results) {
        _handleConnectivityChange(results);
      },
    );
  }

  Future<void> _checkInitialConnection() async {
    try {
      final results = await _connectivity.checkConnectivity();
      _handleConnectivityChange(results);
    } catch (e) {
      debugPrint('Error checking initial connection: $e');
    }
  }

  void _handleConnectivityChange(List<ConnectivityResult> results) {
    results.map(
      (result) {
        final hasDisConnection = result != ConnectivityResult.mobile ||
            result != ConnectivityResult.wifi;
        if (hasDisConnection) {
          _showNoInternetDialog();
          return;
        }
        _hideNoInternetDialog();
      },
    );
  }

  void _showNoInternetDialog() {
    final context = AppRouter.navigatorKey.currentContext;
    if (context == null || !context.mounted) return;

    WidgetsBinding.instance.addPostFrameCallback(
      (_) {
        final currentContext = AppRouter.navigatorKey.currentContext;
        if (currentContext == null || !currentContext.mounted) {
          return;
        }

        showDialog(
          context: currentContext,
          barrierDismissible: false,
          builder: (dialogContext) {
            _dialogContext = dialogContext;
            return PopScope(
              canPop: false,
              child: AlertDialog(
                backgroundColor: AppColors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16.r),
                ),
                title: Column(
                  children: [
                    context.verticalSpaceHigh,
                    Lottie.asset(
                      Assets.lotties.noConnection.path,
                      width: context.width * 0.5,
                      height: context.height * 0.2,
                      fit: BoxFit.cover,
                    ),
                    Text(
                      AppString.noInternetTitle,
                      style: AppTextStyle.bold(18.sp),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
                content: Text(
                  AppString.noInternetContent,
                  textAlign: TextAlign.center,
                  style: AppTextStyle.regular(14.sp),
                ),
              ),
            );
          },
        );
      },
    );
  }

  void _hideNoInternetDialog() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_dialogContext != null && _dialogContext!.mounted) {
        Navigator.of(_dialogContext!, rootNavigator: false).pop();
        _dialogContext = null;
        return;
      }
    });
  }

  void dispose() {
    _connectivitySubscription?.cancel();
    _connectivitySubscription = null;
  }
}
