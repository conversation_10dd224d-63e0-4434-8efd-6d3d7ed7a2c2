import 'app_language.dart';

Map<String, String> enLanguage = {
  AppLanguage.errorDefault: 'Something went wrong',
  AppLanguage.phoneNumber: 'Phone Number',
  AppLanguage.enterPhoneNumber: 'Enter your phone number',
  AppLanguage.password: 'Password',
  AppLanguage.enterPassword: 'Enter your password',
  AppLanguage.login: 'Login',
  AppLanguage.logout: 'Logout',
  AppLanguage.rememberMe: 'Remember me',
  AppLanguage.continueText: 'Continue',
  AppLanguage.contactCustomerCare: "Contact customer care",
  AppLanguage.verifyCapcha: "Plese verify captcha",
  AppLanguage.invalidPhoneNumber: "Invalid phone number",
  AppLanguage.tooManyRequests:
      "Too many requests. Please try again later or contact support.",
  AppLanguage.verificationFailed: "Verification failed",
  AppLanguage.resendOTP: "Resend OTP",
  AppLanguage.enterOTP: "Enter OTP",
  AppLanguage.verifyOTP: "Verify OTP",
  AppLanguage.verifyOTPSendToPhone: "Verify OTP sent to phone number",
  AppLanguage.didNotReceiveOTP: "Didn't receive OTP?",
  AppLanguage.resendSeconds: "Resend in @seconds seconds",
  AppLanguage.invalidOtpCode: 'Invalid OTP code. Please try again.',
  AppLanguage.sessionExpired:
      'Authentication session has expired. Please request a new OTP.',
  AppLanguage.invalidVerificationId:
      'Invalid or expired verification code. Please try again.',
  AppLanguage.networkError:
      'Network error. Please check your internet connection.',
  AppLanguage.unknownError: 'An unknown error occurred',
  AppLanguage.invalidPhoneNumberError: 'Invalid phone number',
  AppLanguage.tooManyRequestsError: 'Too many requests, please try again later',
  AppLanguage.verificationFailedError: 'Verification failed',
  AppLanguage.enterPoint: "Please enter point!",
  AppLanguage.enterChooseUser: "Please choose user!",
  AppLanguage.enterEmail: "Please enter email!",
  AppLanguage.enterPhone: "Please enter phone!",
  AppLanguage.invalidEmail: "Invalid email!",
  AppLanguage.enterValidPhone: "Please enter valid phone number!",
  AppLanguage.invalidPoint: "Invalid point!",
  AppLanguage.pointMustBePositive: "Point must be positive!",
  AppLanguage.onlyAcceptSpecificValues: "Only accept 0, 0.25, 0.5, 0.75...",
  AppLanguage.pointCannotExceedMaximum:
      "Point cannot exceed maximum @point point",
  AppLanguage.home: "Home",
  AppLanguage.activity: "Activity",
  AppLanguage.profile: "Profile",
  AppLanguage.onGoing: "On going",
  AppLanguage.history: "History",
  AppLanguage.detail: "Detail",
  AppLanguage.orderNumber: "Order number",
  AppLanguage.cancel: "Cancel",
  AppLanguage.confirm: "Confirm",
  AppLanguage.noInternetTitle: "No internet",
  AppLanguage.noInternetContent:
      "Please check your internet connection and try again.",
  AppLanguage.startPoint: "Please select start point!",
  AppLanguage.endPoint: "Please select end point!",
  AppLanguage.enterSeat: "Please select number of seat!",
  AppLanguage.edit: "Edit",
  AppLanguage.create: "Create",
  AppLanguage.pickUpPlace: "Pick up place",
  AppLanguage.destinationPlace: "Destination place",
  AppLanguage.pleasePickUpPlace: "Please select pick up place!",
  AppLanguage.pleaseDestinationPlace: "Please select destination place!",
  AppLanguage.transport: "Transport",
  AppLanguage.openMap: "Open map",
  AppLanguage.titleTransport: "Wherever your're going, let's get you there!",
  AppLanguage.whereTo: "Where to?",
  AppLanguage.titleHome: "Welcome",
  AppLanguage.recentPlace: "Recent place",
  AppLanguage.userRegistration: "User registration",
  AppLanguage.confirmPassword: "Confirm password",
  AppLanguage.passwordNotMatch: "Password not match",
  AppLanguage.enterConfirmPassword: "Please enter confirm password!",
  AppLanguage.email: "Email",
  AppLanguage.fullName: "Full name",
  AppLanguage.enterFullName: "Please enter full name!",
  AppLanguage.register: "Register",
  AppLanguage.currentLocation: "Current location",
  AppLanguage.destinationLocation: "Destination location",
  AppLanguage.searchPlace: "Search place",
  AppLanguage.listSearchPlace: "List search place",
};
