class AppLanguage {
  AppLanguage._();
  static const String errorDefault = 'Something went wrong';
  static const String phoneNumber = 'Phone Number';
  static const String enterPhoneNumber = 'Enter your phone number';
  static const String password = 'Password';
  static const String enterPassword = 'Enter your password';
  static const String login = 'Login';
  static const String logout = 'Logout';
  static const String rememberMe = 'Remember me';
  static const String continueText = 'Continue';
  static const String contactCustomerCare = "Contact customer care";
  static const String verifyCapcha = "Plese verify captcha";
  static const String invalidPhoneNumber = "Invalid phone number";
  static const String tooManyRequests =
      "Too many requests. Please try again later or contact support.";
  static const String verificationFailed = "Verification failed";
  static const String resendOTP = "Resend OTP";
  static const String enterOTP = "Enter OTP";
  static const String verifyOTP = "Verify OTP";
  static const String verifyOTPSendToPhone = "Verify OTP sent to phone number";
  static const String didNotReceiveOTP = "Didn't receive OTP?";
  static const String resendSeconds = "resend in @seconds seconds";
  static const String invalidOtpCode = 'invalidOtpCode';
  static const String sessionExpired = 'sessionExpired';
  static const String invalidVerificationId = 'invalidVerificationId';
  static const String networkError = 'networkError';
  static const String unknownError = 'unknownError';
  static const String invalidPhoneNumberError = 'invalidPhoneNumberError';
  static const String tooManyRequestsError = 'tooManyRequestsError';
  static const String verificationFailedError = 'verificationFailedError';
  static const String enterPoint = "enterPoint";
  static const String enterChooseUser = "enterChooseUser";
  static const String enterEmail = "enterEmail";
  static const String enterPhone = "enterPhone";
  static const String invalidEmail = "invalidEmail";
  static const String enterValidPhone = "enterValidPhone";
  static const String invalidPoint = "invalidPoint";
  static const String pointMustBePositive = "pointMustBePositive";
  static const String onlyAcceptSpecificValues = "onlyAcceptSpecificValues";
  static const String pointCannotExceedMaximum = "pointCannotExceedMaximum";
  static const String home = "home";
  static const String activity = "activity";
  static const String profile = "profile";
  static const String onGoing = "onGoing";
  static const String history = "history";
  static const String detail = "detail";
  static const String orderNumber = "orderNumber";
  static const String cancel = "cancel";
  static const String confirm = "confirm";
  static const String noInternetTitle = "noInternetTitle";
  static const String noInternetContent = "noInternetContent";
  static const String startPoint = "startPoint";
  static const String endPoint = "endPoint";
  static const String enterSeat = "enterSeat";
  static const String edit = "edit";
  static const String create = "create";
  static const String pickUpPlace = "pickUpPlace";
  static const String destinationPlace = "destinationPlace";
  static const String pleasePickUpPlace = "pleasePickUpPlace";
  static const String pleaseDestinationPlace = "pleaseDestinationPlace";
  static const String transport = "transport";
  static const String openMap = "openMap";
  static const String titleTransport = "titleTransport";
  static const String whereTo = "whereTo";
  static const String titleHome = "titleHome";
  static const String recentPlace = "recentPlace";
  static const String userRegistration = "userRegistration";
  static const String confirmPassword = "confirmPassword";
  static const String passwordNotMatch = "passwordNotMatch";
  static const String enterConfirmPassword = "enterConfirmPassword";
  static const String email = "email";
  static const String fullName = "fullName";
  static const String enterFullName = "enterFullName";
  static const String register = "register";
  static const String currentLocation = "currentLocation";
  static const String destinationLocation = "destinationLocation";
  static const String searchPlace = "searchPlace";
  static const String listSearchPlace = "listSearchPlace";
}
