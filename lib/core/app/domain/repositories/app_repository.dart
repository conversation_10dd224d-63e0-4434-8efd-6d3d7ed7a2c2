import 'package:base_app/core/utils/task_either_util.dart';
import 'package:base_app/core/app/data/datasources/app_remote.dart';
import 'package:base_app/core/app/data/models/decode_key_respone.dart';
import 'package:base_app/core/app/data/models/app_response.dart';
import 'package:injectable/injectable.dart';

@LazySingleton()
class AppRepository {
  final AppRemote remoteDataSource;

  AppRepository(this.remoteDataSource);

  FutureEither<AppResponse<DecodeKeyRespone>> getDecodeKey() {
    return TaskEitherUtils.fromFuture(
      remoteDataSource.getDecodeKey(),
    );
  }
}
