import 'package:base_app/core/constants/api_constants.dart';
import 'package:base_app/core/app/data/models/decode_key_respone.dart';
import 'package:base_app/core/app/data/models/app_response.dart';
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:retrofit/retrofit.dart';

part 'app_remote.g.dart';

@RestApi()
@LazySingleton()
abstract class AppRemote {
  @factoryMethod
  factory AppRemote(Dio dio) = _AppRemote;

  @GET(ApiConstants.getDecodeKey)
  Future<AppResponse<DecodeKeyRespone>> getDecodeKey();
}
