import 'package:freezed_annotation/freezed_annotation.dart';

part 'app_response.g.dart';

@JsonSerializable(explicitToJson: true, genericArgumentFactories: true)
class AppResponse<T> {
  final bool success;
  final List<dynamic> message;
  final T data;

  AppResponse({
    required this.success,
    required this.message,
    required this.data,
  });

  factory AppResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$AppResponseFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object? Function(T value) toJsonT) =>
      _$AppResponseToJson(this, toJsonT);
}
