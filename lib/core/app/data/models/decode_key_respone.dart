import 'package:freezed_annotation/freezed_annotation.dart';

part 'decode_key_respone.g.dart';

@JsonSerializable(explicitToJson: true)
class DecodeKeyRespone {
  @JsonKey(name: 'MAP_BOX_KEY')
  final String mapBoxKey;

  DecodeKeyRespone({
    required this.mapBoxKey,
  });

  factory DecodeKeyRespone.fromJson(Map<String, dynamic> json) =>
      _$DecodeKeyResponeFromJson(json);

  Map<String, dynamic> toJson() => _$DecodeKeyResponeToJson(this);
}
