import 'package:freezed_annotation/freezed_annotation.dart';

part 'dynamic_note_model.g.dart';

@JsonSerializable(explicitToJson: true)
class DynamicNoteResponse {
  final String? code;
  final String? innerHtml;

  DynamicNoteResponse({
    this.code,
    this.innerHtml,
  });

  factory DynamicNoteResponse.fromJson(Map<String, dynamic> json) =>
      _$DynamicNoteResponseFromJson(json);

  Map<String, dynamic> toJson() => _$DynamicNoteResponseToJson(this);
}
