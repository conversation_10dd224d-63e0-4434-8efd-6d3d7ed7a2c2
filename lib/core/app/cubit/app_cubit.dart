import 'package:base_app/core/di/injection.dart';
import 'package:base_app/core/enums/app_enum.dart';
import 'package:base_app/core/local_storage/i_local_storage_service.dart';
import 'package:base_app/core/app/domain/repositories/app_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';

part 'app_state.dart';

@LazySingleton()
class AppCubit extends Cubit<AppState> {
  AppCubit() : super(AppState());
  AppRepository repository = getIt<AppRepository>();
  ILocalStorageService localStoreService = getIt<ILocalStorageService>();

  Future<void> getDecodeKey() async {
    final response = await repository.getDecodeKey();

    response.fold(
      (failure) {
        emit(state.copyWith(decodeKeyStatus: DecodeKeyStatus.failure));
      },
      (data) {
        final mapBoxKey = data.data.mapBoxKey;
        localStoreService.setMapBoxKeyDecoded(mapBoxKey);
        emit(state.copyWith(decodeKeyStatus: DecodeKeyStatus.success));
      },
    );
  }
}
