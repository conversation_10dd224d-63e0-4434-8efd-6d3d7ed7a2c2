// ignore_for_file: constant_identifier_names

import 'package:flutter/foundation.dart';
import 'package:rxdart/rxdart.dart';

const String _DEFAULT_IDENTIFIER = "apkdev_eventbus_default_identifier";

class Bus {
  late PublishSubject _busSubject;
  late String _tag;
  int _listenerCount = 0;

  PublishSubject get subject => _busSubject;

  String get tag => _tag;

  int get listenerCount => _listenerCount;

  Bus(String tag) {
    _tag = tag;
    _busSubject = PublishSubject();
  }

  Bus.create() {
    _busSubject = PublishSubject();
    _tag = _DEFAULT_IDENTIFIER;
  }

  void incrementListenerCount() {
    _listenerCount++;
  }

  void decrementListenerCount() {
    if (_listenerCount > 0) {
      _listenerCount--;
    }
  }
}

class RxBus {
  static final RxBus _singleton = RxBus._internal();

  factory RxBus() {
    return _singleton;
  }

  RxBus._internal();

  static final List<Bus?> _list = [];

  static RxBus get singleton => _singleton;

  /// 监听事件。每次监听开启都会新建一个[PublishSubject] 防止重复监听事件
  static register<T>({String? tag}) {
    if (tag != null && tag == _DEFAULT_IDENTIFIER) {
      throw FlutterError(
          'EventBus register tag Can\'t is $_DEFAULT_IDENTIFIER ');
    }
    Bus? eventBus;
    //已经注册过的tag不需要重新注册
    if (_list.isNotEmpty && tag != null) {
      for (var bus in _list) {
        if (bus?.tag == tag) {
          eventBus = bus;
          continue;
        }
      }
      if (eventBus == null) {
        eventBus = Bus(tag);
        _list.add(eventBus);
      }
    } else {
      eventBus = tag == null ? Bus.create() : Bus(tag);
      _list.add(eventBus);
    }

    if (T == dynamic) {
      eventBus.subject.stream;
    } else {
      // Increment listener count
      eventBus.incrementListenerCount();
      return eventBus.subject.stream
          .where((event) => event is T)
          .cast<T>()
          .doOnCancel(() {
        eventBus?.decrementListenerCount();
      });
    }
  }

  ///发送事件
  static void post(event, {tag}) {
    for (var rxBus in _list) {
      if (tag != null && tag != _DEFAULT_IDENTIFIER && rxBus?.tag == tag) {
        if (rxBus?.listenerCount != 0) {
          rxBus?.subject.sink.add(event);
        }
      } else if ((tag == null || tag == _DEFAULT_IDENTIFIER) &&
          rxBus?.tag == _DEFAULT_IDENTIFIER) {
        if (rxBus?.listenerCount != 0) {
          rxBus?.subject.sink.add(event);
        }
      }
    }
  }

  ///事件关闭
  static void destroy({tag}) {
    var toRemove = [];
    for (var rxBus in _list) {
      if (tag != null && tag != _DEFAULT_IDENTIFIER && rxBus?.tag == tag) {
        rxBus?.subject.close();
        toRemove.add(rxBus);
      } else if ((tag == null || tag == _DEFAULT_IDENTIFIER) &&
          rxBus?.tag == _DEFAULT_IDENTIFIER) {
        rxBus?.subject.close();
        toRemove.add(rxBus);
      }
    }
    for (var rxBus in toRemove) {
      _list.remove(rxBus);
    }
  }

  static bool hasListeners({String? tag}) {
    for (var rxBus in _list) {
      if ((tag != null && tag != _DEFAULT_IDENTIFIER && rxBus?.tag == tag) ||
          (tag == null || tag == _DEFAULT_IDENTIFIER) &&
              rxBus?.tag == _DEFAULT_IDENTIFIER) {
        return rxBus?.listenerCount != 0;
      }
    }
    return false;
  }
}
