import 'package:base_app/core/extension/app_extension_context.dart';
import 'package:base_app/core/theme/app_text_styles.dart';
import 'package:base_app/core/widgets/app_animated_button.dart';
import 'package:base_app/gen/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AppCopy extends StatelessWidget {
  final String text;
  final Function()? onTap;
  const AppCopy({
    super.key,
    required this.text,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return AppAnimatedButton(
      onTap: onTap,
      child: Row(
        children: [
          Text(
            text,
            style: AppTextStyle.regular(),
          ),
          context.horizontalSpaceSmall,
          Assets.icons.iconCopy.image(width: 24.dm, height: 24.dm),
        ],
      ),
    );
  }
}
