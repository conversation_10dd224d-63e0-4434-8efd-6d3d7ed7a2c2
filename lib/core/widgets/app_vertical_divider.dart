import 'package:base_app/core/theme/app_colors.dart';
import 'package:flutter/material.dart';

class AppVerticalDivider extends StatelessWidget {
  final double height;
  final double width;
  final Color color;
  final EdgeInsetsGeometry? margin;
  const AppVerticalDivider({
    super.key,
    this.height = 30,
    this.width = 2,
    this.color = AppColors.gray400,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      width: width,
      height: height,
      color: color,
    );
  }
}
