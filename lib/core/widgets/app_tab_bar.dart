import 'package:base_app/core/theme/app_colors.dart';
import 'package:base_app/core/theme/app_text_styles.dart';
import 'package:flutter/material.dart';

class AppTabBar extends StatefulWidget {
  final List<String> tabs;
  final List<Widget> tabContents;

  const AppTabBar({
    super.key,
    required this.tabs,
    required this.tabContents,
  });

  @override
  State<AppTabBar> createState() => _AppTabBarState();
}

class _AppTabBarState extends State<AppTabBar> {
  final List<GlobalKey> _tabKeys = [];

  final ValueNotifier<int> _currentIndexNotifier = ValueNotifier(0);

  @override
  void initState() {
    super.initState();
    for (int i = 0; i < widget.tabs.length; i++) {
      _tabKeys.add(GlobalKey());
    }
  }

  @override
  Widget build(BuildContext context) {
    var size = MediaQuery.of(context).size;

    return ValueListenableBuilder(
      valueListenable: _currentIndexNotifier,
      builder: (context, currentIndex, _) {
        return Column(
          children: [
            Container(
              width: size.width,
              height: 50,
              decoration: BoxDecoration(
                color: AppColors.white,
                boxShadow: [
                  BoxShadow(
                    color: AppColors.gray.withValues(alpha: 0.1),
                    spreadRadius: 1,
                    blurRadius: 3,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Stack(
                children: [
                  Row(
                    children: List.generate(widget.tabs.length, (index) {
                      bool isSelected = currentIndex == index;
                      return Expanded(
                        child: GestureDetector(
                          key: _tabKeys[index],
                          onTap: () {
                            _currentIndexNotifier.value = index;
                          },
                          child: Container(
                            color: Colors.transparent,
                            alignment: Alignment.center,
                            child: Text(
                              widget.tabs[index],
                              style: AppTextStyle.medium(16).copyWith(
                                color: isSelected
                                    ? AppColors.primaryDark
                                    : AppColors.primaryDark
                                        .withValues(alpha: 0.5),
                                fontWeight: isSelected
                                    ? FontWeight.w600
                                    : FontWeight.w400,
                              ),
                            ),
                          ),
                        ),
                      );
                    }),
                  ),

                  AnimatedPositioned(
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Row(
                      children: List.generate(widget.tabs.length, (index) {
                        bool isSelected = currentIndex == index;
                        return Expanded(
                          child: AnimatedContainer(
                            duration: const Duration(milliseconds: 300),
                            curve: Curves.easeInOut,
                            height: 3,
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? AppColors.honoluluBlue
                                  : AppColors.gray,
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                        );
                      }),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: IndexedStack(
                index: currentIndex,
                children: widget.tabContents,
              ),
            ),
          ],
        );
      },
    );
  }
}
