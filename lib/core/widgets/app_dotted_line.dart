import 'package:base_app/core/theme/app_colors.dart';
import 'package:flutter/material.dart';

class AppDottedLine extends StatelessWidget {
  final double height;
  final Color color;

  const AppDottedLine({
    super.key,
    this.height = 1,
    this.color = AppColors.philippineGray,
  });

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: Dotted<PERSON>inePainter(color: color),
      size: Size(double.infinity, height),
    );
  }
}

class DottedLinePainter extends CustomPainter {
  final Color color;

  DottedLinePainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    var paint = Paint()
      ..color = color
      ..strokeWidth = 1;

    var max = size.width;
    var dashWidth = 5;
    var dashSpace = 3;
    double startX = 0;

    while (startX < max) {
      canvas.drawLine(
        Offset(startX, 0),
        Offset(startX + dashWidth, 0),
        paint,
      );
      startX += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
