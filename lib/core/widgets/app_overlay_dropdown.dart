import 'package:base_app/core/extension/app_extension_context.dart';
import 'package:base_app/core/theme/app_colors.dart';
import 'package:base_app/core/theme/app_text_styles.dart';
import 'package:base_app/core/widgets/app_animated_button.dart';
import 'package:flutter/material.dart';

class AppOverlayDropdown extends StatefulWidget {
  final List<DropdownItem> items;
  final String hintText;
  final ValueChanged<DropdownItem> onChanged;
  const AppOverlayDropdown({
    super.key,
    required this.items,
    required this.hintText,
    required this.onChanged,
  });
  @override
  State<AppOverlayDropdown> createState() => _AppOverlayDropdownState();
}

class _AppOverlayDropdownState extends State<AppOverlayDropdown> {
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  final ValueNotifier<String?> _selectedValue = ValueNotifier(null);
  final ValueNotifier<bool> _isDropdownOpen = ValueNotifier(false);

  @override
  void dispose() {
    _removeOverlay();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: _isDropdownOpen,
      builder: (context, isDropdownOpen, _) {
        return CompositedTransformTarget(
          link: _layerLink,
          child: GestureDetector(
            onTap: () => _toggleDropdown(isDropdownOpen),
            child: Container(
              padding: context.paddingHigh,
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.gray400),
                borderRadius: context.borderRadiusMedium,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  ValueListenableBuilder(
                    valueListenable: _selectedValue,
                    builder: (context, selectedValue, _) {
                      return Text(
                        selectedValue ?? widget.hintText,
                        style: AppTextStyle.regular().copyWith(
                          color: selectedValue == null
                              ? AppColors.taupeGray
                              : AppColors.black,
                        ),
                      );
                    },
                  ),
                  Icon(
                    isDropdownOpen
                        ? Icons.arrow_drop_up
                        : Icons.arrow_drop_down,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  void _toggleDropdown(bool isOpen) {
    isOpen ? _removeDropdown() : _showDropdown();
  }

  void _showDropdown() {
    final overlay = Overlay.of(context);
    final renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;
    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        width: size.width,
        child: CompositedTransformFollower(
          link: _layerLink,
          offset: Offset(0, size.height + 4),
          child: Material(
            elevation: 1,
            color: AppColors.white,
            borderRadius: context.borderRadiusMedium,
            child: ValueListenableBuilder(
              valueListenable: _selectedValue,
              builder: (context, selectedValue, _) {
                return ListView.builder(
                  shrinkWrap: true,
                  padding: EdgeInsets.zero,
                  itemCount: widget.items.length,
                 
                  itemBuilder: (context, index) {
                    final item = widget.items[index];
                    final isSelected = selectedValue == item.name;
                    return AppAnimatedButton(
                      onTap: () {
                        _selectedValue.value = item.name;
                        widget.onChanged(item);
                        _removeDropdown();
                      },
                      child: Container(
                        padding: context.paddingHigh,
                        decoration: BoxDecoration(
                          color: isSelected
                              ? AppColors.honoluluBlue.withValues(alpha: 0.1)
                              : null,
                        ),
                        child: Text(
                          item.name,
                          style: AppTextStyle.regular().copyWith(
                            color: isSelected ? AppColors.honoluluBlue : null,
                          ),
                        ),
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ),
      ),
    );
    overlay.insert(_overlayEntry!);
    _isDropdownOpen.value = true;
  }

  void _removeDropdown() {
    _isDropdownOpen.value = false;
    if (_overlayEntry == null) return;
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _removeOverlay() {
    if (_overlayEntry == null) return;
    _overlayEntry?.remove();
    _overlayEntry = null;
  }
}

class DropdownItem {
  final String name;
  DropdownItem({
    required this.name,
  });
}
