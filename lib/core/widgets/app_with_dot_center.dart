import 'package:base_app/core/extension/app_extension_context.dart';
import 'package:base_app/core/theme/app_text_styles.dart';
import 'package:flutter/material.dart';

class AppWithDotCenter extends StatelessWidget {
  final String contentLeft;
  final String contentRight;
  final TextStyle? styleLeft;
  final TextStyle? styleRight;
  const AppWithDotCenter({
    super.key,
    required this.contentLeft,
    required this.contentRight,
    this.styleLeft,
    this.styleRight,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text(
          contentLeft,
          style: styleLeft ?? AppTextStyle.medium(),
        ),
        context.horizontalSpaceSmall,
        Text(
          '•',
        ),
        context.horizontalSpaceSmall,
        Text(
          contentRight,
          style: styleRight ?? AppTextStyle.medium(),
        ),
      ],
    );
  }
}
