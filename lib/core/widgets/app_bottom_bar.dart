import 'package:animated_bottom_navigation_bar/animated_bottom_navigation_bar.dart';
import 'package:base_app/core/di/injection.dart';
import 'package:base_app/core/extension/app_extension_context.dart';
import 'package:base_app/core/rx_service/rx_bus.dart';
import 'package:base_app/core/rx_service/rx_events.dart';
import 'package:base_app/core/theme/app_colors.dart';
import 'package:base_app/core/theme/app_text_styles.dart';
import 'package:base_app/features/main/cubit/bottom_bar_cubit.dart';
import 'package:base_app/gen/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:auto_size_text/auto_size_text.dart';

class NavBarItem {
  NavBarItem({
    required this.icon,
    required this.iconActive,
    required this.onTap,
    required this.title,
  });
  final AssetGenImage icon;
  final AssetGenImage iconActive;
  final String title;
  final Function(int) onTap;
}

class AppBottomBar extends StatefulWidget {
  const AppBottomBar({
    required this.items,
    super.key,
  });
  final List<NavBarItem> items;

  @override
  State<AppBottomBar> createState() => _AppBottomBarState();
}

class _AppBottomBarState extends State<AppBottomBar> {
  late BottomBarCubit _cubit;

  @override
  void initState() {
    _cubit = getIt<BottomBarCubit>();
    _registerChangeCurrentTab();
    super.initState();
  }

  _registerChangeCurrentTab() {
    RxBus.register<ChangeCurrentTab>(tag: RxBusTag.changeCurrentTab)
        .listen((ChangeCurrentTab event) async {
      _cubit.changeCurrentTab(event.currentTab);
    });
    RxBus.register<NaviTab>(tag: RxBusTag.naviTab)
        .listen((NaviTab event) async {
      _cubit.changeCurrentTab(0);
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BottomBarCubit, BottomBarState>(
      bloc: _cubit,
      builder: (context, state) {
        if (state.isVisible == false) {
          return const SizedBox();
        }
        return Container(
          decoration: BoxDecoration(
            boxShadow: [
              BoxShadow(
                color: AppColors.cgBlue.withValues(alpha: 0.1),
                spreadRadius: 0,
                blurRadius: 4,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: AnimatedBottomNavigationBar.builder(
            safeAreaValues: SafeAreaValues(bottom: true),
            itemCount: widget.items.length,
            notchMargin: 0,
            tabBuilder: (index, isActive) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  context.verticalSpaceSmall,
                  isActive
                      ? widget.items[index].iconActive
                          .image(width: 24.dm, height: 24.dm)
                      : widget.items[index].icon
                          .image(width: 24.dm, height: 24.dm),
                  context.verticalSpaceSmall,
                  AutoSizeText(
                    widget.items[index].title,
                    minFontSize: 10,
                    maxFontSize: 13,
                    style: AppTextStyle.medium(14).copyWith(
                      color: isActive
                          ? AppColors.honoluluBlue
                          : AppColors.philippineGray,
                    ),
                  ),
                ],
              );
            },
            splashRadius: 0,
            backgroundGradient: const LinearGradient(
              colors: [
                AppColors.white,
                AppColors.white,
                AppColors.white,
                AppColors.white
              ],
              begin: FractionalOffset.topCenter,
              end: FractionalOffset.bottomCenter,
              stops: [0.0, 0.1, 0.9, 1.0],
            ),
            activeIndex: state.bottomNaviIndex,
            splashColor: AppColors.white,
            splashSpeedInMilliseconds: 300,
            notchSmoothness: NotchSmoothness.softEdge,
            gapLocation: GapLocation.none,
            leftCornerRadius: 0,
            rightCornerRadius: 0,
            onTap: (index) {
              _cubit.changeCurrentTab(index);
              widget.items[index].onTap.call(index);
            },
          ),
        );
      },
    );
  }
}
