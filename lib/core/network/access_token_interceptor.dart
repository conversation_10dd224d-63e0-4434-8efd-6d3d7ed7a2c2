import 'package:base_app/core/di/injection.dart';
import 'package:base_app/core/local_storage/i_local_storage_service.dart';
import 'package:dio/dio.dart';

class AccessTokenInterceptor extends Interceptor {
  @override
  Future<dynamic> onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    ILocalStorageService localStoreService = getIt<ILocalStorageService>();
    String token = await localStoreService.getAccessToken();
    if (token.isNotEmpty) {
      options.headers['Authorization'] = 'Bearer $token';
    }
    return super.onRequest(options, handler);
  }
}
