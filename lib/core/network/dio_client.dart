import 'package:base_app/core/constants/api_constants.dart';
import 'package:base_app/core/network/access_token_interceptor.dart';
import 'package:base_app/core/network/logging_interceptor.dart';
import 'package:base_app/core/network/unauthorized_interceptor.dart';
import 'package:base_app/core/config/flavor_config.dart';
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';

@lazySingleton
class DioClient {
  late final Dio _dio;

  Dio get dio => _dio;

  void init() {
    _dio = Dio(
      BaseOptions(
        baseUrl: FlavorConfig.apiBaseUrl,
        connectTimeout: const Duration(
          milliseconds: ApiConstants.connectTimeout,
        ),
        receiveTimeout: const Duration(
          milliseconds: ApiConstants.receiveTimeout,
        ),
      ),
    );
    _dio.interceptors.addAll(
      [
        AccessTokenInterceptor(),
        UnauthorizedInterceptor(),
        LoggingInterceptor(),
      ],
    );
  }
}
