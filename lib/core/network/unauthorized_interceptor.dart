import 'package:base_app/core/constants/api_constants.dart';
import 'package:base_app/core/di/injection.dart';
import 'package:base_app/core/local_storage/i_local_storage_service.dart';
import 'package:base_app/core/network/dio_client.dart';
import 'package:base_app/core/config/flavor_config.dart';
import 'package:dio/dio.dart';


class UnauthorizedInterceptor extends Interceptor {
  static const int maxRetryCount = 3;
  static bool _isRefreshing = false;
  static final List<({ErrorInterceptorHandler handler, RequestOptions options})>
      _requestQueue = [];

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    final requestOptions = err.requestOptions;

    if (err.response?.statusCode == 401) {
      await _handleUnauthorizedError(handler, requestOptions);
    } else {
      handler.next(err);
    }
  }

  Future<void> _handleUnauthorizedError(
      ErrorInterceptorHandler handler, RequestOptions requestOptions) async {
    final localStoreService = getIt<ILocalStorageService>();

    if (_isRefreshing) {
      _requestQueue.add((handler: handler, options: requestOptions));
      return;
    }

    final refreshToken = await localStoreService.getRefreshToken();

    if (refreshToken.isEmpty) {
      _logoutAndReset(handler);
      return;
    }

    final retryCount = requestOptions.extra['retryCount'] ?? 0;
    if (retryCount >= maxRetryCount) {
      _logoutAndReset(handler);
      return;
    }

    _isRefreshing = true;

    try {
      await _refreshToken(refreshToken);
      final newToken = await localStoreService.getAccessToken();

      await _retryRequest(handler, requestOptions, newToken, retryCount);

      await _retryQueuedRequests(newToken);
    } catch (error) {
      _logoutAndReset(handler);
      _rejectQueuedRequests();
    } finally {
      _isRefreshing = false;
      _requestQueue.clear();
    }
  }

  Future<void> _refreshToken(String token) async {
    final dio = Dio(BaseOptions(
      baseUrl: FlavorConfig.apiBaseUrl,
      connectTimeout: const Duration(milliseconds: ApiConstants.connectTimeout),
      receiveTimeout: const Duration(milliseconds: ApiConstants.receiveTimeout),
      headers: {'Authorization': 'Bearer $token'},
    ));

    final response = await dio.post('auth/refresh-token');
    final data = response.data['data'];

    await getIt<ILocalStorageService>().setAccessToken(data['accessToken']);
    await getIt<ILocalStorageService>().setRefreshToken(data['refreshToken']);
  }

  Future<void> _retryRequest(
      ErrorInterceptorHandler handler,
      RequestOptions requestOptions,
      String newToken,
      int currentRetryCount) async {
    try {

      final updatedOptions = requestOptions.copyWith(
        extra: {
          ...requestOptions.extra,
          'retryCount': currentRetryCount + 1,
        },
      );

      final clonedOptions = Options(
        method: updatedOptions.method,
        headers: {
          'Authorization': 'Bearer $newToken',
          ...updatedOptions.headers,
        },
        sendTimeout: const Duration(milliseconds: ApiConstants.connectTimeout),
        receiveTimeout: const Duration(milliseconds: ApiConstants.receiveTimeout),
        extra: updatedOptions.extra,
      );

      final response = await getIt<DioClient>().dio.request(
        updatedOptions.path,
        options: clonedOptions,
        data: updatedOptions.data,
        queryParameters: updatedOptions.queryParameters,
      );

      handler.resolve(response);
    } on DioException catch (err) {
      handler.next(err);
    }
  }

  Future<void> _retryQueuedRequests(String newToken) async {
    final requests = List.from(_requestQueue);

    for (final request in requests) {
      try {
        await _retryRequest(request.handler, request.options, newToken, 0);
      } catch (error) {
        request.handler.next(DioException(requestOptions: request.options));
      }
    }
  }

  void _rejectQueuedRequests() {
    for (final request in _requestQueue) {
      request.handler.next(DioException(requestOptions: request.options));
    }
  }

  void _logoutAndReset(ErrorInterceptorHandler handler) {
    // getIt<AuthCubit>().logout();
    // LoadingManager.instance.hideLoading();
    handler.next(DioException(requestOptions: RequestOptions(path: '')));
  }
}
