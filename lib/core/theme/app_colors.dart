import 'package:flutter/material.dart';

class AppColors {
  AppColors._();

  static const Color secondary = Color(0xFF66BB6A);
  static const Color accent = Color(0xFFFFD54F);
  static const Color error = Color.fromRGBO(211, 47, 47, 1);
  static const Color white = Colors.white;
  static const Color black = Colors.black;
  static const Color background = Color(0xFFFFFFFF);
  static const Color background1 = Color(0xFFF5F9FF);
  static const Color brightGray = Color(0xFFECECEC);
  static const Color cgBlue = Color(0xFF007A99);
  static const Color cosmicLatte = Color(0xFFFEFBE8);
  static const Color darkCharcoal = Color(0xFF333333);
  static const Color darkMantis = Color(0xFF40823E);
  static const Color gray = Color(0xFFEEEEEE);
  static const Color gray400 = Color(0xFFBDBDBD);
  static const Color gray600 = Color(0xFF757575);
  static const Color honoluluBlue = Color(0xFF005DB2);
  static const Color illuminatingEmerald = Color(0xFF379777);
  static const Color ivory = Color(0xFFF8FFEE);
  static const Color lightGray = Color(0xFFD9D9D9);
  static const Color lust = Color(0xFFE82020);
  static const Color mantis = Color(0xFF6CC26B);
  static const Color maximumYellowRed = Color(0xFFEFB041);
  static const Color oxfordBlue = Color(0xFF00284C);
  static const Color pastelOrange = Color(0xFFFCAA4E);
  static const Color philippineGray = Color(0xFF8D8D8D);
  static const Color primary = Color(0xFF005DB2);
  static const Color primaryBt = Color(0xFFF5F878);
  static const Color primaryDark = Color(0xFF032525);
  static const Color red = Color(0xFFFF0303);
  static const Color seashell = Color(0xFFFDF2F0);
  static const Color taupeGray = Color(0xFF888888);
  static const Color vampireBlack = Color(0xFF050505);
  static const Color water = Color(0xFFCAF0F8);
}
