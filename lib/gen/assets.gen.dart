// dart format width=80

/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: deprecated_member_use,directives_ordering,implicit_dynamic_list_literal,unnecessary_import

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart' as _svg;
import 'package:lottie/lottie.dart' as _lottie;
import 'package:vector_graphics/vector_graphics.dart' as _vg;

class $AssetsFontsGen {
  const $AssetsFontsGen();

  /// File path: assets/fonts/Roboto-Bold.ttf
  String get robotoBold => 'assets/fonts/Roboto-Bold.ttf';

  /// File path: assets/fonts/Roboto-Italic.ttf
  String get robotoItalic => 'assets/fonts/Roboto-Italic.ttf';

  /// File path: assets/fonts/Roboto-Light.ttf
  String get robotoLight => 'assets/fonts/Roboto-Light.ttf';

  /// File path: assets/fonts/Roboto-Medium.ttf
  String get robotoMedium => 'assets/fonts/Roboto-Medium.ttf';

  /// File path: assets/fonts/Roboto-Regular.ttf
  String get robotoRegular => 'assets/fonts/Roboto-Regular.ttf';

  /// List of all assets
  List<String> get values =>
      [robotoBold, robotoItalic, robotoLight, robotoMedium, robotoRegular];
}

class $AssetsIconsGen {
  const $AssetsIconsGen();

  /// File path: assets/icons/home.png
  AssetGenImage get home => const AssetGenImage('assets/icons/home.png');

  /// File path: assets/icons/home_pick.png
  AssetGenImage get homePick =>
      const AssetGenImage('assets/icons/home_pick.png');

  /// File path: assets/icons/icon_clock_gray.png
  AssetGenImage get iconClockGray =>
      const AssetGenImage('assets/icons/icon_clock_gray.png');

  /// File path: assets/icons/icon_clock_pick.png
  AssetGenImage get iconClockPick =>
      const AssetGenImage('assets/icons/icon_clock_pick.png');

  /// File path: assets/icons/icon_copy.png
  AssetGenImage get iconCopy =>
      const AssetGenImage('assets/icons/icon_copy.png');

  /// File path: assets/icons/icon_destination.png
  AssetGenImage get iconDestination =>
      const AssetGenImage('assets/icons/icon_destination.png');

  /// File path: assets/icons/icon_direct_up.png
  AssetGenImage get iconDirectUp =>
      const AssetGenImage('assets/icons/icon_direct_up.png');

  /// File path: assets/icons/icon_driving_gray.png
  AssetGenImage get iconDrivingGray =>
      const AssetGenImage('assets/icons/icon_driving_gray.png');

  /// File path: assets/icons/icon_driving_pick copy.png
  AssetGenImage get iconDrivingPickCopy =>
      const AssetGenImage('assets/icons/icon_driving_pick copy.png');

  /// File path: assets/icons/icon_driving_pick.png
  AssetGenImage get iconDrivingPick =>
      const AssetGenImage('assets/icons/icon_driving_pick.png');

  /// File path: assets/icons/icon_hide.png
  AssetGenImage get iconHide =>
      const AssetGenImage('assets/icons/icon_hide.png');

  /// File path: assets/icons/icon_logo.png
  AssetGenImage get iconLogo =>
      const AssetGenImage('assets/icons/icon_logo.png');

  /// File path: assets/icons/icon_profile.png
  AssetGenImage get iconProfile =>
      const AssetGenImage('assets/icons/icon_profile.png');

  /// File path: assets/icons/icon_profile_gray.png
  AssetGenImage get iconProfileGray =>
      const AssetGenImage('assets/icons/icon_profile_gray.png');

  /// File path: assets/icons/icon_trash.png
  AssetGenImage get iconTrash =>
      const AssetGenImage('assets/icons/icon_trash.png');

  /// File path: assets/icons/icon_view.png
  AssetGenImage get iconView =>
      const AssetGenImage('assets/icons/icon_view.png');

  /// List of all assets
  List<AssetGenImage> get values => [
        home,
        homePick,
        iconClockGray,
        iconClockPick,
        iconCopy,
        iconDestination,
        iconDirectUp,
        iconDrivingGray,
        iconDrivingPickCopy,
        iconDrivingPick,
        iconHide,
        iconLogo,
        iconProfile,
        iconProfileGray,
        iconTrash,
        iconView
      ];
}

class $AssetsLottiesGen {
  const $AssetsLottiesGen();

  /// File path: assets/lotties/404_error.json
  LottieGenImage get a404Error =>
      const LottieGenImage('assets/lotties/404_error.json');

  /// File path: assets/lotties/go.json
  LottieGenImage get go => const LottieGenImage('assets/lotties/go.json');

  /// File path: assets/lotties/loading.json
  LottieGenImage get loading =>
      const LottieGenImage('assets/lotties/loading.json');

  /// File path: assets/lotties/no_connection.json
  LottieGenImage get noConnection =>
      const LottieGenImage('assets/lotties/no_connection.json');

  /// List of all assets
  List<LottieGenImage> get values => [a404Error, go, loading, noConnection];
}

class $AssetsSvgsGen {
  const $AssetsSvgsGen();

  /// File path: assets/svgs/bell.svg
  SvgGenImage get bell => const SvgGenImage('assets/svgs/bell.svg');

  /// File path: assets/svgs/hide.svg
  SvgGenImage get hide => const SvgGenImage('assets/svgs/hide.svg');

  /// File path: assets/svgs/history.svg
  SvgGenImage get history => const SvgGenImage('assets/svgs/history.svg');

  /// File path: assets/svgs/location.svg
  SvgGenImage get location => const SvgGenImage('assets/svgs/location.svg');

  /// File path: assets/svgs/map.svg
  SvgGenImage get map => const SvgGenImage('assets/svgs/map.svg');

  /// File path: assets/svgs/search.svg
  SvgGenImage get search => const SvgGenImage('assets/svgs/search.svg');

  /// File path: assets/svgs/view.svg
  SvgGenImage get view => const SvgGenImage('assets/svgs/view.svg');

  /// List of all assets
  List<SvgGenImage> get values =>
      [bell, hide, history, location, map, search, view];
}

class Assets {
  const Assets._();

  static const $AssetsFontsGen fonts = $AssetsFontsGen();
  static const $AssetsIconsGen icons = $AssetsIconsGen();
  static const $AssetsLottiesGen lotties = $AssetsLottiesGen();
  static const $AssetsSvgsGen svgs = $AssetsSvgsGen();
}

class AssetGenImage {
  const AssetGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
    this.animation,
  });

  final String _assetName;

  final Size? size;
  final Set<String> flavors;
  final AssetGenImageAnimation? animation;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({
    AssetBundle? bundle,
    String? package,
  }) {
    return AssetImage(
      _assetName,
      bundle: bundle,
      package: package,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class AssetGenImageAnimation {
  const AssetGenImageAnimation({
    required this.isAnimation,
    required this.duration,
    required this.frames,
  });

  final bool isAnimation;
  final Duration duration;
  final int frames;
}

class SvgGenImage {
  const SvgGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
  }) : _isVecFormat = false;

  const SvgGenImage.vec(
    this._assetName, {
    this.size,
    this.flavors = const {},
  }) : _isVecFormat = true;

  final String _assetName;
  final Size? size;
  final Set<String> flavors;
  final bool _isVecFormat;

  _svg.SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    String? package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    _svg.SvgTheme? theme,
    _svg.ColorMapper? colorMapper,
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    final _svg.BytesLoader loader;
    if (_isVecFormat) {
      loader = _vg.AssetBytesLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
      );
    } else {
      loader = _svg.SvgAssetLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
        theme: theme,
        colorMapper: colorMapper,
      );
    }
    return _svg.SvgPicture(
      loader,
      key: key,
      matchTextDirection: matchTextDirection,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      colorFilter: colorFilter ??
          (color == null ? null : ColorFilter.mode(color, colorBlendMode)),
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class LottieGenImage {
  const LottieGenImage(
    this._assetName, {
    this.flavors = const {},
  });

  final String _assetName;
  final Set<String> flavors;

  _lottie.LottieBuilder lottie({
    Animation<double>? controller,
    bool? animate,
    _lottie.FrameRate? frameRate,
    bool? repeat,
    bool? reverse,
    _lottie.LottieDelegates? delegates,
    _lottie.LottieOptions? options,
    void Function(_lottie.LottieComposition)? onLoaded,
    _lottie.LottieImageProviderFactory? imageProviderFactory,
    Key? key,
    AssetBundle? bundle,
    Widget Function(
      BuildContext,
      Widget,
      _lottie.LottieComposition?,
    )? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    double? width,
    double? height,
    BoxFit? fit,
    AlignmentGeometry? alignment,
    String? package,
    bool? addRepaintBoundary,
    FilterQuality? filterQuality,
    void Function(String)? onWarning,
    _lottie.LottieDecoder? decoder,
    _lottie.RenderCache? renderCache,
    bool? backgroundLoading,
  }) {
    return _lottie.Lottie.asset(
      _assetName,
      controller: controller,
      animate: animate,
      frameRate: frameRate,
      repeat: repeat,
      reverse: reverse,
      delegates: delegates,
      options: options,
      onLoaded: onLoaded,
      imageProviderFactory: imageProviderFactory,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      package: package,
      addRepaintBoundary: addRepaintBoundary,
      filterQuality: filterQuality,
      onWarning: onWarning,
      decoder: decoder,
      renderCache: renderCache,
      backgroundLoading: backgroundLoading,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
