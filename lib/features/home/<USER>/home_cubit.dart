import 'package:base_app/core/enums/app_enum.dart';
import 'package:base_app/features/home/<USER>/models/place_detail_response.dart';
import 'package:base_app/features/home/<USER>/models/place_reponse.dart';
import 'package:base_app/features/home/<USER>/repositories/home_repository.dart';
import 'package:base_app/core/local_storage/i_local_storage_service.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:base_app/core/di/injection.dart';
import 'package:injectable/injectable.dart';

part 'home_state.dart';

@LazySingleton()
class HomeCubit extends Cubit<HomeState> {
  HomeCubit() : super(HomeState());
  HomeRepository repository = getIt<HomeRepository>();
  ILocalStorageService localStorageService = getIt<ILocalStorageService>();

  void getPlaces({required String place}) async {
    emit(state.copyWith(getPlacesStatus: GetPlacesStatus.loading));
    final response = await repository.getPlaces(place: place);

    response.fold(
      (failure) {
        emit(
          state.copyWith(
            getPlacesStatus: GetPlacesStatus.failure,
            errorMessage: failure.message,
          ),
        );
      },
      (response) {
        final places = response.data;
        emit(
          state.copyWith(
            getPlacesStatus: GetPlacesStatus.success,
            places: places,
          ),
        );
      },
    );
  }

  void setCurrentPlace(PlaceReponse place) {
    emit(state.copyWith(currentPlace: place));
  }

  void setDestinationPlace(PlaceReponse place) {
    emit(state.copyWith(destinationPlace: place));
  }

  void loadHistoryPlaces() async {
    final historyPlaces = await localStorageService.getPlaceSelected();
    emit(
      state.copyWith(
        historyPlaces: historyPlaces.toSet().toList(),
      ),
    );
  }

  void getPlaceDetail({required String placeId}) async {
    final response = await repository.getPlaceDetail(placeId: placeId);

    response.fold(
      (failure) {
        emit(
          state.copyWith(
            getPlaceDetailStatus: GetPlaceDetailStatus.failure,
            errorMessage: failure.message,
          ),
        );
      },
      (response) {
        final place = response.data;
        emit(
          state.copyWith(
            placeDetail: place,
            getPlaceDetailStatus: GetPlaceDetailStatus.success,
          ),
        );
      },
    );
  }
}
