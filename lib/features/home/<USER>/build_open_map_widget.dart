import 'package:base_app/core/extension/app_extension_context.dart';
import 'package:base_app/core/theme/app_colors.dart';
import 'package:base_app/core/widgets/app_animated_button.dart';
import 'package:base_app/gen/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BuildOpenMapWidget extends StatelessWidget {
  final Function()? onTap;
  const BuildOpenMapWidget({
    super.key,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return AppAnimatedButton(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(context.valuePaddingNormal),
        decoration: BoxDecoration(
          borderRadius: context.borderRadiusMedium,
          color: AppColors.white,
        ),
        child: Assets.svgs.map.svg(
          width: 24.dm,
          height: 24.dm,
          colorFilter: ColorFilter.mode(
            AppColors.honoluluBlue,
            BlendMode.srcIn,
          ),
        ),
      ),
    );
  }
}
