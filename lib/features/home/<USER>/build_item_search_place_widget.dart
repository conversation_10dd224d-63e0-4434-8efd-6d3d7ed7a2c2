import 'package:base_app/core/extension/app_extension_context.dart';
import 'package:base_app/core/theme/app_colors.dart';
import 'package:base_app/core/theme/app_text_styles.dart';
import 'package:base_app/core/utils/safe_navigation_util.dart';
import 'package:base_app/core/widgets/app_animated_button.dart';
import 'package:base_app/features/home/<USER>/models/place_reponse.dart';
import 'package:base_app/gen/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class BuildItemSearchPlaceWidget extends StatelessWidget {
  final PlaceReponse place;
  final bool isHistory;
  final Function(PlaceReponse)? callBack;
  const BuildItemSearchPlaceWidget({
    super.key,
    this.callBack,
    required this.place,
    this.isHistory = false,
  });

  @override
  Widget build(BuildContext context) {
    return AppAnimatedButton(
      onTap: () {
        callBack?.call(place);
        SafeNavigationUtil.pop(context);
      },
      child: Padding(
        padding:  EdgeInsets.symmetric(
          vertical: context.valuePaddingNormal,
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: EdgeInsets.all(context.valuePaddingNormal),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.honoluluBlue.withValues(alpha: 0.1),
              ),
              child: SvgPicture.asset(
                isHistory ? Assets.svgs.history.path : Assets.svgs.location.path,
                width: 24.dm,
                height: 24.dm,
                colorFilter: ColorFilter.mode(
                  AppColors.honoluluBlue,
                  BlendMode.srcIn,
                ),
              ),
            ),
            context.horizontalSpaceNormal,
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    place.name ?? '',
                    style: AppTextStyle.semibold(),
                  ),
                  context.verticalSpaceNormal,
                  Text(
                    place.address ?? '',
                    style: AppTextStyle.regular().copyWith(
                      color: AppColors.gray600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
