import 'package:base_app/features/home/<USER>/models/place_reponse.dart';
import 'package:base_app/features/home/<USER>/build_item_search_place_widget.dart';
import 'package:flutter/material.dart';

class RecentPlaceWidget extends StatelessWidget {
  final List<PlaceReponse> historyPlaces;

  final Function(PlaceReponse)? onTap;
  final bool isHistory;

  const RecentPlaceWidget({
    super.key,
    required this.historyPlaces,
    this.onTap,
    this.isHistory = true,
  });

  @override
  Widget build(BuildContext context) {
    if (historyPlaces.isEmpty) {
      return const SizedBox();
    }
    return Expanded(
      child: ListView.builder(
        padding: EdgeInsets.zero,
        itemCount: historyPlaces.length,
        itemBuilder: (context, index) {
          final place = historyPlaces[index];
          return BuildItemSearchPlaceWidget(
            isHistory: isHistory,
            callBack: (place) {
              onTap?.call(place);
            },
            place: place,
          );
        },
      ),
    );
  }
}
