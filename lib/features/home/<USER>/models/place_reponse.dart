
import 'package:json_annotation/json_annotation.dart';

part 'place_reponse.g.dart';

@JsonSerializable(explicitToJson: true)
class PlaceReponse {
  final String? name;
  final String? address;
  final String? placeId;

  PlaceReponse({
    this.name,
    this.address,
    this.placeId,
  });

  factory PlaceReponse.fromJson(Map<String, dynamic> json) =>
      _$PlaceReponseFromJson(json);

  Map<String, dynamic> toJson() => _$PlaceReponseToJson(this);
}