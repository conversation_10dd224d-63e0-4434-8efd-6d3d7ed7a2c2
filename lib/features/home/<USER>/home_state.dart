part of 'home_cubit.dart';

class HomeState {
  const HomeState({
    this.getPlacesStatus = GetPlacesStatus.initial,
    this.places = const [],
    this.historyPlaces = const [],
    this.errorMessage,
    this.currentPlace,
    this.destinationPlace,
    this.placeCurrentDetail,
    this.placeDestinationDetail,
    this.getPlaceDetailStatus = GetPlaceDetailStatus.initial,
  });

  final GetPlacesStatus getPlacesStatus;
  final List<PlaceReponse> places;
  final List<PlaceReponse> historyPlaces;
  final String? errorMessage;
  final PlaceReponse? currentPlace;
  final PlaceReponse? destinationPlace;
  final PlaceDetailResponse? placeCurrentDetail;
  final PlaceDetailResponse? placeDestinationDetail;
  final GetPlaceDetailStatus getPlaceDetailStatus;

  HomeState copyWith({
    GetPlacesStatus? getPlacesStatus,
    List<PlaceReponse>? places,
    List<PlaceReponse>? historyPlaces,
    String? errorMessage,
    PlaceReponse? currentPlace,
    PlaceReponse? destinationPlace,
    PlaceDetailResponse? placeCurrentDetail,
    PlaceDetailResponse? placeDestinationDetail,
    GetPlaceDetailStatus? getPlaceDetailStatus,
  }) {
    return HomeState(
      getPlacesStatus: getPlacesStatus ?? this.getPlacesStatus,
      places: places ?? this.places,
      historyPlaces: historyPlaces ?? this.historyPlaces,
      errorMessage: errorMessage ?? this.errorMessage,
      currentPlace: currentPlace ?? this.currentPlace,
      destinationPlace: destinationPlace ?? this.destinationPlace,
      placeCurrentDetail: placeCurrentDetail ?? this.placeCurrentDetail,
      getPlaceDetailStatus: getPlaceDetailStatus ?? this.getPlaceDetailStatus,
      placeDestinationDetail:
          placeDestinationDetail ?? this.placeDestinationDetail,
    );
  }
}
