import 'package:base_app/core/app/data/models/app_response.dart';
import 'package:base_app/core/constants/api_constants.dart';
import 'package:base_app/features/home/<USER>/models/place_detail_response.dart';
import 'package:base_app/features/home/<USER>/models/place_reponse.dart';
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:retrofit/retrofit.dart';

part 'home_remote.g.dart';

@RestApi()
@LazySingleton()
abstract class HomeRemote {
  @factoryMethod
  factory HomeRemote(Dio dio) = _HomeRemote;

  @GET(ApiConstants.getPlaces)
  Future<AppResponse<List<PlaceReponse>>> getPlaces({
    @Query(ApiConstants.input) String? place,
  });


  @GET(ApiConstants.placeDetail)
  Future<AppResponse<PlaceDetailResponse>> getPlaceDetail({
    @Query(ApiConstants.placeId) String? placeId,
  });
}
