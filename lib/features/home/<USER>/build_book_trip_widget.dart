import 'package:base_app/core/widgets/app_map.dart';
import 'package:flutter/material.dart';

class BuildBookTripWidget extends StatefulWidget {
  const BuildBookTripWidget({super.key});

  @override
  State<BuildBookTripWidget> createState() => _BuildBookTripWidgetState();
}

class _BuildBookTripWidgetState extends State<BuildBookTripWidget> {
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        AppMapbox(),
        BottomSheet(
          onClosing: () {},
          builder: (context) {
            return Container(
              height: 200,
              color: Colors.red,
            );
          },
        )
      ],
    );
  }
}