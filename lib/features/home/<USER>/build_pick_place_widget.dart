import 'package:base_app/core/extension/app_extension_context.dart';
import 'package:base_app/core/theme/app_colors.dart';
import 'package:base_app/core/theme/app_text_styles.dart';
import 'package:base_app/core/widgets/app_animated_button.dart';
import 'package:base_app/gen/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BuildPickPlaceWidget extends StatelessWidget {
  final Function()? onTap;
  final String title;
  final String? name;
  final String? address;
  final Color colorIcon;
  const BuildPickPlaceWidget({
    super.key,
    this.onTap,
    required this.title,
    this.name,
    this.address,
    this.colorIcon = AppColors.honoluluBlue,
  });

  @override
  Widget build(BuildContext context) {
    return AppAnimatedButton(
      onTap: onTap,
      child: Container(
        padding: context.paddingMedium,
        decoration: BoxDecoration(
          borderRadius: context.borderRadiusMedium,
          border: Border.all(
            color: AppColors.gray400,
          ),
        ),
        child: Row(
          children: [
            Assets.svgs.location.svg(
              width: 24.dm,
              height: 24.dm,
              colorFilter: ColorFilter.mode(
                colorIcon,
                BlendMode.srcIn,
              ),
            ),
            context.horizontalSpaceNormal,
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTextStyle.regular(14.sp),
                  ),
                  if (name?.isNotEmpty == true) context.verticalSpaceNormal,
                  if (name?.isNotEmpty == true)
                    Text(
                      name!,
                      style: AppTextStyle.medium(),
                    ),
                  if (address?.isNotEmpty == true) context.verticalSpaceNormal,
                  if (address?.isNotEmpty == true)
                    Text(
                      address!,
                      style: AppTextStyle.regular(12.sp).copyWith(
                        color: AppColors.philippineGray,
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
