import 'package:base_app/core/extension/app_extension_context.dart';
import 'package:base_app/core/theme/app_text_styles.dart';
import 'package:base_app/core/translation/app_string.dart';
import 'package:base_app/core/utils/safe_navigation_util.dart';
import 'package:base_app/router/app_router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'build_notification_widget.dart';
import 'build_open_map_widget.dart';

class BuildHeaderWidget extends StatelessWidget {
  const BuildHeaderWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppString.titleHome,
              style: AppTextStyle.regular(),
            ),
            context.verticalSpaceNormal,
            Text(
              "Tên người dùng",
              style: AppTextStyle.bold(24.sp),
            ),
          ],
        ),
        Row(
          children: [
            BuildOpenMapWidget(
              onTap: () => SafeNavigationUtil.push(context, AppRouter.map),
            ),
            BuildNotificationWidget(),
          ],
        )
      ],
    );
  }
}
