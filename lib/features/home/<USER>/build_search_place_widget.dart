import 'dart:async';

import 'package:base_app/core/di/injection.dart';
import 'package:base_app/core/enums/app_enum.dart';
import 'package:base_app/core/extension/app_extension_context.dart';
import 'package:base_app/core/local_storage/i_local_storage_service.dart';
import 'package:base_app/core/theme/app_colors.dart';
import 'package:base_app/core/theme/app_text_styles.dart';
import 'package:base_app/core/translation/app_string.dart';
import 'package:base_app/core/utils/safe_navigation_util.dart';
import 'package:base_app/core/widgets/app_animated_button.dart';
import 'package:base_app/core/widgets/app_input.dart';
import 'package:base_app/features/home/<USER>/home_cubit.dart';
import 'package:base_app/features/home/<USER>/models/place_reponse.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'build_item_search_place_widget.dart';
import 'recent_place_widget.dart';

class BuildSearchPlaceWidget extends StatefulWidget {
  final String title;
  final Function(PlaceReponse)? onTap;
  const BuildSearchPlaceWidget({
    super.key,
    required this.title,
    this.onTap,
  });

  @override
  State<BuildSearchPlaceWidget> createState() => _BuildSearchPlaceWidgetState();
}

class _BuildSearchPlaceWidgetState extends State<BuildSearchPlaceWidget> {
  TextEditingController searchCtrl = TextEditingController();
  Timer? _debounce;
  HomeCubit homeCubit = getIt<HomeCubit>();
  ILocalStorageService localStorageService = getIt<ILocalStorageService>();

  FocusNode searchFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    homeCubit.loadHistoryPlaces();
  }

  @override
  void dispose() {
    searchFocusNode.dispose();
    searchCtrl.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeCubit, HomeState>(
      bloc: homeCubit,
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            context.appBarHeight.verticalSpace,
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  widget.title,
                  style: AppTextStyle.medium(20),
                ),
                AppAnimatedButton(
                  onTap: () => SafeNavigationUtil.pop(context),
                  child: const Icon(Icons.close),
                )
              ],
            ),
            AppTextFormField(
              focusNode: searchFocusNode,
              controller: searchCtrl,
              hintText: AppString.searchPlace,
              onChanged: (p0) {
                if (_debounce?.isActive ?? false) _debounce!.cancel();
                _debounce = Timer(const Duration(milliseconds: 700), () {
                  if (p0.isNotEmpty && p0.trim().isNotEmpty) {
                    homeCubit.getPlaces(place: p0);
                  }
                });
              },
            ),
            context.verticalSpaceHigh,
            if (searchCtrl.text.isEmpty) ...[
              Text(AppString.recentPlace, style: AppTextStyle.medium()),
              context.verticalSpaceLarge,
              RecentPlaceWidget(
                historyPlaces: state.historyPlaces,
                onTap: (place) {
                  widget.onTap?.call(place);
                },
              ),
            ],
            if (state.getPlacesStatus == GetPlacesStatus.loading) ...[
              context.verticalSpaceHigh,
              const Center(
                child: CircularProgressIndicator(
                  color: AppColors.honoluluBlue,
                ),
              )
            ] else ...[
              context.verticalSpaceHigh,
              if (state.getPlacesStatus == GetPlacesStatus.success &&
                  searchCtrl.text.isNotEmpty) ...[
                Text(AppString.listSearchPlace, style: AppTextStyle.medium()),
                context.verticalSpaceHigh,
                Expanded(
                  child: ListView.separated(
                    itemCount: state.places.length,
                    separatorBuilder: (_, __) => context.verticalSpaceHigh,
                    itemBuilder: (context, index) {
                      final place = state.places[index];

                      return BuildItemSearchPlaceWidget(
                        callBack: (place) async {
                          final currentHistory =
                              await localStorageService.getPlaceSelected();

                          List<PlaceReponse> updatedHistory = [place];
                          for (final historyPlace in currentHistory) {
                            if (historyPlace.placeId != place.placeId) {
                              updatedHistory.add(historyPlace);
                            }
                          }

                          if (updatedHistory.length > 10) {
                            updatedHistory = updatedHistory.take(10).toList();
                          }

                          await localStorageService
                              .savePlaceSelected(updatedHistory);

                          homeCubit.loadHistoryPlaces();

                          widget.onTap?.call(place);
                        },
                        place: place,
                      );
                    },
                  ),
                ),
              ],
            ],
          ],
        );
      },
    );
  }
}
