import 'package:base_app/core/di/injection.dart';
import 'package:base_app/core/translation/app_string.dart';
import 'package:base_app/core/utils/loading_overlay_util.dart';
import 'package:base_app/core/utils/safe_navigation_util.dart';
import 'package:base_app/core/widgets/app_snack_bar.dart';
import 'package:base_app/features/login/cubit/login_cubit.dart';
import 'package:base_app/features/login/widget/build_login_form.dart';
import 'package:base_app/router/app_router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  late LoginCubit _loginCubit;

  @override
  void initState() {
    super.initState();
    _loginCubit = getIt<LoginCubit>();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<LoginCubit, LoginState>(
          bloc: _loginCubit,
          listener: _handleLoginState,
        ),
      ],
      child: BuildLoginForm(
        loginCubit: _loginCubit,
      ),
    );
  }

  void _handleLoginState(BuildContext context, LoginState state) {
    if (_isLoading(state)) {
      AppLoadingOverlay.show(context);
      return;
    }

    AppLoadingOverlay.hide();

    if (_isSuccess(state)) {
      SafeNavigationUtil.go(context, AppRouter.main);
      return;
    }

    if (state.successRecaptcha == false) {
      _showRecaptchaWarning(context);
    } else if (_isFailure(state)) {
      _handleLoginFailure(context, state);
    }
  }

  bool _isLoading(LoginState state) {
    return state.loginStatus == LoginStatus.loading ||
        state.checkAccountExistStatus == CheckAccountExistStatus.loading ||
        state.verifyCapchaStatus == VerifyCapchaStatus.loading;
  }

  bool _isSuccess(LoginState state) {
    return state.loginStatus == LoginStatus.success;
  }

  bool _isFailure(LoginState state) {
    return state.loginStatus == LoginStatus.failure ||
        state.checkAccountExistStatus == CheckAccountExistStatus.failure;
  }

  void _showRecaptchaWarning(BuildContext context) {
    AppSnackBar.warning(
      context,
      message: AppString.verifyCapcha,
    );
  }

  void _handleLoginFailure(BuildContext context, LoginState state) {
    if (state.textErr?.isNotEmpty ?? false) {
      AppSnackBar.error(
        context,
        message: state.textErr!,
      );
    }
    _loginCubit.resetLoginState();
  }
}
