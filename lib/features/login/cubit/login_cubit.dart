import 'package:base_app/core/di/injection.dart';
import 'package:base_app/core/local_storage/i_local_storage_service.dart';
import 'package:base_app/features/login/data/models/login_request.dart';
import 'package:base_app/features/login/data/models/user_model.dart';
import 'package:base_app/features/login/domain/repositories/login_repository.dart';
import 'package:dio/dio.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';

part 'login_state.dart';

@Injectable()
class LoginCubit extends Cubit<LoginState> {
  LoginCubit() : super(const LoginState());
  ILocalStorageService localStoreService = getIt<ILocalStorageService>();
  LoginRepository repository = getIt<LoginRepository>();

  Future<void> login({required LoginRequest request}) async {
    emit(state.copyWith(loginStatus: LoginStatus.loading));

    try {
      //get fcm token in storage
      // String savedFCMToken = await localStoreService.getAccessToken();
      // String? deviceId = '';
      // if (StringUtils.isNullOrEmpty(savedFCMToken)) {
      //   // request premission if is ios
      //   if (Platform.isIOS) {
      //     FirebaseMessaging messaging = FirebaseMessaging.instance;

      //     NotificationSettings settings = await messaging.requestPermission(
      //       alert: true,
      //       announcement: false,
      //       badge: true,
      //       carPlay: false,
      //       criticalAlert: false,
      //       sound: true,
      //     );

      //     if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      //       // For apple platforms, ensure the APNS token is available before making any FCM plugin API calls
      //       final apnsToken = await FirebaseMessaging.instance.getAPNSToken();
      //       if (apnsToken != null) {
      //         deviceId = await FirebaseMessaging.instance.getToken();
      //       }
      //     }
      //   } else {
      //     deviceId = await FirebaseMessaging.instance.getToken();
      //   }
      // } else {
      //   deviceId = savedFCMToken;
      // }
      final response = await repository.loginMobile(request: request);

      response.fold(
        (failure) {
          emit(
            state.copyWith(
              loginStatus: LoginStatus.failure,
              textErr: failure.message.toString(),
            ),
          );
        },
        (response) {
          final accessToken = response.data.accessToken ?? '';
          final user = response.data.me ?? UserModel();
          final refreshToken = response.data.refreshToken ?? '';
          localStoreService.saveCredentials(accessToken, user);
          //save fcm token
          //getIt<ILocalStorageService>().setFCMToken(deviceId ?? '');
          getIt<ILocalStorageService>().setRefreshToken(refreshToken);
          //save remember phone
          setLocalRememberPhone();
          emit(state.copyWith(loginStatus: LoginStatus.success));
        },
      );
    } on DioException catch (e) {
      emit(
        state.copyWith(
          loginStatus: LoginStatus.failure,
          textErr: e.response?.data?['message']?[0]['msg']?.toString(),
        ),
      );
    }
  }

  void checkAccountExist({required String phone}) async {
    emit(
      state.copyWith(
        checkAccountExistStatus: CheckAccountExistStatus.loading,
      ),
    );
    try {
      final response = await repository.checkAccountExist(phoneNumber: phone);
      localStoreService.setUserPhone(phone);

      response.fold(
        (failure) {
          emit(
            state.copyWith(
              checkAccountExistStatus: CheckAccountExistStatus.failure,
              textErr: failure.message.toString(),
            ),
          );
        },
        (response) {
          emit(
            state.copyWith(
              checkAccountExistStatus: CheckAccountExistStatus.success,
              isAccountExisted: response.data,
              userName: phone,
            ),
          );
        },
      );
    } on DioException catch (e) {
      emit(
        state.copyWith(
          checkAccountExistStatus: CheckAccountExistStatus.failure,
          textErr: e.response?.data?['message']?[0]['msg']?.toString(),
        ),
      );
    }
  }

  void checkPhoneNumberSaved({required String phone}) async {
    String phoneSaved = await localStoreService.getUserPhone();
    if (phone != phoneSaved &&
        state.isAccountExisted == true &&
        state.checkAccountExistStatus == CheckAccountExistStatus.success) {
      emit(state.copyWith(
          isAccountExisted: false,
          checkAccountExistStatus: CheckAccountExistStatus.initial));
    }
  }

  void resetState() {
    emit(const LoginState());
  }

  void resetVerificationState() {
    emit(state.copyWith(
      checkAccountExistStatus: CheckAccountExistStatus.initial,
      verifyCapchaStatus: VerifyCapchaStatus.initial,
      successRecaptcha: null,
      isAccountExisted: null,
    ));
  }

  void resetLoginState() {
    emit(
      state.copyWith(
        loginStatus: LoginStatus.initial,
        textErr: null,
      ),
    );
  }

  void setLocalRememberPhone() {
    localStoreService.setRememberPhone(state.isRememberPhone);
    localStoreService.setPhoneNumerRemember(state.userName ?? '');
  }

  void onChangeRemember(bool? value) async {
    emit(
      state.copyWith(
        isRememberPhone: value,
      ),
    );
  }

  Future<bool> getRememberPhoneLocal() async {
    bool value = await localStoreService.getRememberPhone();
    String? phone = await localStoreService.getPhoneNumerRemember();
    if (!isClosed) {
      emit(
        state.copyWith(
          isRememberPhone: value,
          phoneRemember: phone,
        ),
      );
    }
    return value;
  }

  // Future<void> verifyCapcha() async {
  //   try {
  //     emit(state.copyWith(verifyCapchaStatus: VerifyCapchaStatus.loading));
  //     final siteKey = Platform.isAndroid
  //         ? AppConstants.siteKeyAndroid
  //         : AppConstants.siteKeyIOS;
  //     RecaptchaClient client = await Recaptcha.fetchClient(siteKey);

  //     final operatingSystem = Platform.operatingSystem;

  //     String token = await client.execute(RecaptchaAction.SIGNUP());
  //     final response = await repository
  //         .verifyCapcha(
  //             request: CapchaRequest(
  //           token: token,
  //           operatingSystem: operatingSystem,
  //         ))
  //         .run();

  //     response.fold(
  //       (failure) {
  //         emit(
  //           state.copyWith(
  //             verifyCapchaStatus: VerifyCapchaStatus.failure,
  //             successRecaptcha: null,
  //             textErr: failure.message.toString(),
  //           ),
  //         );
  //       },
  //       (success) {
  //         emit(
  //           state.copyWith(
  //             successRecaptcha: success,
  //             verifyCapchaStatus: VerifyCapchaStatus.success,
  //           ),
  //         );
  //       },
  //     );
  //   } on DioException catch (e) {
  //     final statusCode = e.response?.statusCode;
  //     emit(
  //       state.copyWith(
  //         verifyCapchaStatus: VerifyCapchaStatus.failure,
  //         successRecaptcha: null,
  //         textErr: statusCode.toString(),
  //       ),
  //     );
  //   }
  // }

  void showOrHidePassword(bool obscureText) {
    emit(
      state.copyWith(
        obscureText: !obscureText,
      ),
    );
  }

  void setSummited(bool summited) {
    emit(
      state.copyWith(
        summited: summited,
      ),
    );
  }
}
