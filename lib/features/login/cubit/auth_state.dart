part of 'auth_cubit.dart';

enum AuthenticationStatus { unknown, authenticated, unauthenticated }

enum LogoutStatus { initial, loading, success, failure }

enum LoginStateStatus { initial, loading, success, failure }

class AuthState {
  const AuthState(
      {this.loginStatus = LoginStateStatus.initial,
      this.status = AuthenticationStatus.unknown,
      this.logoutStatus = LogoutStatus.initial,
      this.user,
      this.textErr})
      : super();

  final LoginStateStatus loginStatus;
  final AuthenticationStatus status;
  final LogoutStatus logoutStatus;
  final UserModel? user;
  final String? textErr;

  AuthState copyWith(
      {LoginStateStatus? loginStatus,
      AuthenticationStatus? status,
      LogoutStatus? logoutStatus,
      UserModel? user,
      String? textErr}) {
    return AuthState(
      loginStatus: loginStatus ?? this.loginStatus,
      status: status ?? this.status,
      logoutStatus: logoutStatus ?? this.logoutStatus,
      user: user ?? this.user,
      textErr: textErr ?? this.textErr,
    );
  }
}
