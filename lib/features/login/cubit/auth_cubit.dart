import 'package:base_app/core/di/injection.dart';
import 'package:base_app/core/local_storage/i_local_storage_service.dart';
import 'package:base_app/features/login/data/models/user_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';

part 'auth_state.dart';

@LazySingleton()
class AuthCubit extends Cubit<AuthState> {
  ILocalStorageService localStoreService = getIt<ILocalStorageService>();
  AuthCubit() : super(const AuthState()) {
    localStoreService.hasAuthenticated().then((hasAuthenticated) {
      debugPrint('hasAuthenticated $hasAuthenticated');
      _mapAuthenticationStatusChangedToState(hasAuthenticated
          ? AuthenticationStatus.authenticated
          : AuthenticationStatus.unauthenticated);
    });
  }

  Future<void> _mapAuthenticationStatusChangedToState(
      AuthenticationStatus authState) async {
    switch (authState) {
      case AuthenticationStatus.unauthenticated:
        await localStoreService.removeCredentials();
        emit(state.copyWith(status: AuthenticationStatus.unauthenticated));
        return;
      case AuthenticationStatus.authenticated:
        emit(state.copyWith(status: AuthenticationStatus.authenticated));
        return;
      default:
        emit(state.copyWith(status: AuthenticationStatus.unknown));
        return;
    }
  }

  Future<void> logout() async {
    emit(state.copyWith(logoutStatus: LogoutStatus.loading));
    try {
      await localStoreService.clearAll();
      await localStoreService.removeCredentials();
      emit(
        state.copyWith(
          logoutStatus: LogoutStatus.success,
          status: AuthenticationStatus.unauthenticated,
        ),
      );
    } catch (e) {
      emit(state.copyWith(
        textErr: e.toString(),
        logoutStatus: LogoutStatus.failure,
      ));
    }
  }
}
