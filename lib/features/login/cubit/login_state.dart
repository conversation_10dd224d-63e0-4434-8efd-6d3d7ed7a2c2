part of 'login_cubit.dart';

enum LoginStatus { initial, loading, success, failure }

enum CheckAccountExistStatus { initial, loading, success, failure }

enum VerifyCapchaStatus { initial, loading, success, failure }

class LoginState {
  const LoginState(
      {this.loginStatus = LoginStatus.initial,
      this.textErr,
      this.userName,
      this.isAccountExisted,
      this.isRememberPhone = false,
      this.obscureText = true,
      this.summited = false,
      this.phoneRemember,
      this.successRecaptcha,
      this.verifyCapchaStatus = VerifyCapchaStatus.initial,
      this.checkAccountExistStatus = CheckAccountExistStatus.initial})
      : super();

  final LoginStatus loginStatus;
  final String? textErr;
  final String? userName;
  final bool? isAccountExisted;
  final CheckAccountExistStatus? checkAccountExistStatus;
  final VerifyCapchaStatus? verifyCapchaStatus;
  final bool isRememberPhone;
  final String? phoneRemember;
  final bool? successRecaptcha;
  final bool obscureText;
  final bool summited;

  LoginState copyWith(
      {LoginStatus? loginStatus,
      String? textErr,
      String? userName,
      String? phoneRemember,
      bool? isAccountExisted,
      bool? isRememberPhone,
      bool? successRecaptcha,
      bool? obscureText,
      bool? summited,
      VerifyCapchaStatus? verifyCapchaStatus,
      CheckAccountExistStatus? checkAccountExistStatus}) {
    return LoginState(
      loginStatus: loginStatus ?? this.loginStatus,
      textErr: textErr ?? this.textErr,
      userName: userName ?? this.userName,
      isRememberPhone: isRememberPhone ?? this.isRememberPhone,
      phoneRemember: phoneRemember ?? this.phoneRemember,
      isAccountExisted: isAccountExisted ?? this.isAccountExisted,
      checkAccountExistStatus:
          checkAccountExistStatus ?? this.checkAccountExistStatus,
      successRecaptcha: successRecaptcha,
      verifyCapchaStatus: verifyCapchaStatus,
      obscureText: obscureText ?? this.obscureText,
      summited: summited ?? this.summited,
    );
  }
}
