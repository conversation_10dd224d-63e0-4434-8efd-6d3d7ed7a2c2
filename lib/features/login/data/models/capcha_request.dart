import 'package:freezed_annotation/freezed_annotation.dart';

part 'capcha_request.g.dart';

@JsonSerializable(explicitToJson: true)
class CapchaRequest {
  final String operatingSystem;
  final String token;

  CapchaRequest({
    required this.operatingSystem,
    required this.token,
  });

  factory CapchaRequest.fromJson(Map<String, dynamic> json) =>
      _$CapchaRequestFromJson(json);

  Map<String, dynamic> toJson() => _$CapchaRequestToJson(this);
}
