import 'package:freezed_annotation/freezed_annotation.dart';

part 'roles_model.g.dart';

@JsonSerializable(explicitToJson: true)
class RolesModel {
  final int? id;
  final String? name;
  final String? code;

  RolesModel({
    this.id,
    this.name,
    this.code,
  });

  factory RolesModel.fromJson(Map<String, dynamic> json) => _$RolesModelFromJson(json);

  Map<String, dynamic> toJson() => _$RolesModelToJson(this);
}
