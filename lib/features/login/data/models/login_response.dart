import 'package:freezed_annotation/freezed_annotation.dart';

import 'user_model.dart';

part 'login_response.g.dart';

@JsonSerializable(explicitToJson: true)
class LoginResponse {
  final String? tokenType;
  final String? accessToken;
  final String? refreshToken;
  final int? expiresIn;
  final UserModel? me;

  LoginResponse({
    this.tokenType,
    this.accessToken,
    this.refreshToken,
    this.expiresIn,
    this.me,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) =>
      _$LoginResponseFromJson(json);

  Map<String, dynamic> toJson() => _$LoginResponseToJson(this);
}
