import 'package:base_app/core/constants/api_constants.dart';
import 'package:base_app/core/app/data/models/app_response.dart';
import 'package:base_app/features/login/data/models/capcha_request.dart';
import 'package:base_app/features/login/data/models/login_request.dart';
import 'package:base_app/features/login/data/models/login_response.dart';
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:retrofit/retrofit.dart';

part 'login_remote.g.dart';

@RestApi()
@LazySingleton()
abstract class LoginRemote {
  @factoryMethod
  factory LoginRemote(Dio dio) = _LoginRemote;

  @POST(ApiConstants.loginMobile)
  Future<AppResponse<LoginResponse>> loginMobile({
    @Body() required LoginRequest request,
  });

  @GET(ApiConstants.checkAccountExist)
  Future<AppResponse<bool>> checkAccountExist({
    @Query(ApiConstants.queryParamUsername) required String phoneNumber,
  });

  @POST(ApiConstants.verifyCapcha)
  Future<bool> verifyCapcha({
    @Body() required CapchaRequest request,
  });
}
