import 'package:base_app/core/enums/app_enum.dart';
import 'package:base_app/core/extension/app_extension_context.dart';
import 'package:base_app/core/theme/app_text_styles.dart';
import 'package:base_app/core/translation/app_string.dart';
import 'package:base_app/core/utils/safe_navigation_util.dart';
import 'package:base_app/core/utils/validate_util.dart';
import 'package:base_app/core/widgets/app_base.dart';
import 'package:base_app/core/widgets/app_button.dart';
import 'package:base_app/core/widgets/app_input.dart';
import 'package:base_app/features/login/cubit/login_cubit.dart';
import 'package:base_app/features/login/data/models/login_request.dart';
import 'package:base_app/gen/assets.gen.dart';
import 'package:base_app/router/app_router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'build_remember_phone.dart';

class BuildLoginForm extends StatefulWidget {
  final LoginCubit loginCubit;
  const BuildLoginForm({
    super.key,
    required this.loginCubit,
  });

  @override
  State<BuildLoginForm> createState() => _BuildLoginFormState();
}

class _BuildLoginFormState extends State<BuildLoginForm> {
  final Map<String, dynamic> _formData = {};
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController phoneCtrl = TextEditingController();

  @override
  void initState() {
    initData();
    super.initState();
  }

  @override
  void dispose() {
    phoneCtrl.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AppBase(
      showAppBar: false,
      body: BlocBuilder<LoginCubit, LoginState>(
        bloc: widget.loginCubit,
        builder: (context, state) {
          return Form(
            key: _formKey,
            child: Container(
              padding:
                  EdgeInsets.symmetric(horizontal: context.valuePaddingHigh),
              child: Center(
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      context.appBarHeight.verticalSpace,
                      (context.width / 1.5).verticalSpace,
                      // Hero(
                      //   tag: AppConstants.tagHeroLogo,
                      //   child: Assets.icons.iconLogo.image(
                      //     width: context.width / 1.5,
                      //   ),
                      // ),
                      AppTextFormField(
                        controller: phoneCtrl,
                        label: AppString.phoneNumber,
                        hintText: AppString.enterPhoneNumber,
                        keyboardType: TextInputType.number,
                        isRequired: false,
                        submitted: state.summited,
                        onChanged: (value) {
                          widget.loginCubit.checkPhoneNumberSaved(phone: value);
                          _formData['username'] = value;
                        },
                        validator: (value) =>
                            ValidateUtil.instance.requiredField(
                          value: phoneCtrl.text,
                          isRequired: true,
                          type: ValidationType.phone,
                        ),
                      ),
                      context.verticalSpaceMedium,
                      if (state.isAccountExisted == true)
                        AppTextFormField(
                          submitted: state.summited,
                          label: AppString.password,
                          hintText: AppString.enterPassword,
                          isRequired: false,
                          obscureText: state.obscureText,
                          suffixIcon: GestureDetector(
                            onTap: () {
                              widget.loginCubit
                                  .showOrHidePassword(state.obscureText);
                            },
                            child: Padding(
                              padding:
                                  EdgeInsets.all(context.valuePaddingMedium),
                              child: state.obscureText
                                  ? Assets.icons.iconHide.image(height: 24)
                                  : Assets.icons.iconView.image(height: 24),
                            ),
                          ),
                          onChanged: (String value) {
                            _formData['password'] = value;
                          },
                          validator: (value) =>
                              ValidateUtil.instance.requiredField(
                            value: value,
                            isRequired: true,
                            type: ValidationType.password,
                          ),
                        ),
                      if (state.isAccountExisted == true)
                        context.verticalSpaceMedium,
                      BuildRememberPhone(
                        rememberPhoneValue: state.isRememberPhone,
                        onChangedRemember: (value) =>
                            widget.loginCubit.onChangeRemember(value),
                      ),
                      context.verticalSpaceMedium,
                      AppButton(
                        onTap: () => onSubmit(state.isAccountExisted),
                        title: state.isAccountExisted == true
                            ? AppString.login
                            : AppString.continueText,
                      ),
                      (context.width / 3).verticalSpace,
                      context.verticalSpaceMedium,
                      MotionButton(
                        onTap: () => _resetForm(),
                        child: Text(
                          AppString.contactCustomerCare,
                          style: AppTextStyle.medium(14)
                              .copyWith(decoration: TextDecoration.underline),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  void initData() {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      rememberPhone();
    });
  }

  void rememberPhone() async {
    final isRememberPhone = await widget.loginCubit.getRememberPhoneLocal();
    if (!mounted) return;
    final state = widget.loginCubit.state;
    if (isRememberPhone) {
      phoneCtrl.text = state.phoneRemember ?? '';
      _formData['username'] = phoneCtrl.text;
    }
  }

  void _resetForm() {
    widget.loginCubit.setSummited(false);
    _formKey.currentState?.reset();
    _formData.clear();
    SafeNavigationUtil.go(
      context,
      AppRouter.otp,
      extra: {
        'verificationId': '',
        'resendToken': 0,
        'userName': '',
      },
    );
  }

  void onSubmit(bool? isAccountExisted) {
    widget.loginCubit.setSummited(true);
    final bool isValid = _formKey.currentState?.validate() == true;

    if (isValid) {
      if (isAccountExisted == true) {
        _formKey.currentState?.save();
        widget.loginCubit.login(
          request: LoginRequest(
            username: _formData['username'],
            password: _formData['password'],
          ),
        );
      } else {
        widget.loginCubit.resetVerificationState();
        widget.loginCubit.checkAccountExist(
          phone: _formData['username'],
        );
      }
    }
  }
}
