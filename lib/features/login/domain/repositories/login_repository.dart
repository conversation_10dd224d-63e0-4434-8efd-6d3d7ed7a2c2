import 'package:base_app/core/utils/task_either_util.dart';
import 'package:base_app/core/app/data/models/app_response.dart';
import 'package:base_app/features/login/data/datasources/login_remote.dart';
import 'package:base_app/features/login/data/models/capcha_request.dart';
import 'package:base_app/features/login/data/models/login_request.dart';
import 'package:base_app/features/login/data/models/login_response.dart';
import 'package:injectable/injectable.dart';

@LazySingleton()
class LoginRepository implements ILoginRepository {
  final LoginRemote remoteDataSource;

  LoginRepository(this.remoteDataSource);

  @override
  FutureEither<AppResponse<LoginResponse>> loginMobile({
    required LoginRequest request,
  }) {
    return TaskEitherUtils.fromFuture(
      remoteDataSource.loginMobile(request: request),
    );
  }

  @override
  FutureEither<AppResponse<bool>> checkAccountExist({
    required String phoneNumber,
  }) {
    return TaskEitherUtils.fromFuture(
      remoteDataSource.checkAccountExist(phoneNumber: phoneNumber),
    );
  }

  @override
  FutureEither<bool> verifyCapcha({required CapchaRequest request}) {
    return TaskEitherUtils.fromFuture(
      remoteDataSource.verifyCapcha(request: request),
    );
  }
}

abstract class ILoginRepository {
  FutureEither<AppResponse<LoginResponse>> loginMobile({
    required LoginRequest request,
  });

  FutureEither<AppResponse<bool>> checkAccountExist({
    required String phoneNumber,
  });

  FutureEither<bool> verifyCapcha({required CapchaRequest request});
}
