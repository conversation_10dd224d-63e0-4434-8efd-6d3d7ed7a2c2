import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';

part 'bottom_bar_state.dart';

@lazySingleton
class BottomBarCubit extends Cubit<BottomBarState> {
  BottomBarCubit() : super(BottomBarState());

  void isBottomBar({bool? isShow}) {
    emit(state.copyWith(isVisible: isShow));
  }

  void changeCurrentTab(int index) {
    emit(state.copyWith(bottomNaviIndex: index));
  }
}
