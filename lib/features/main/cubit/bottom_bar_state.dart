part of 'bottom_bar_cubit.dart';

class BottomBarState {
  const BottomBarState({
    this.isVisible = true,
    this.bottomNaviIndex = 0,
  }) : super();

  final bool isVisible;
  final int bottomNaviIndex;

  BottomBarState copyWith({
    bool? isVisible,
    int? bottomNaviIndex,
  }) {
    return BottomBarState(
      isVisible: isVisible ?? this.isVisible,
      bottomNaviIndex: bottomNaviIndex ?? this.bottomNaviIndex,
    );
  }
}
