import 'package:base_app/core/app/cubit/app_cubit.dart';
import 'package:base_app/core/di/injection.dart';
import 'package:base_app/core/enums/app_enum.dart';
import 'package:base_app/core/helper/app_hepler.dart';
import 'package:base_app/core/local_storage/i_local_storage_service.dart';
import 'package:base_app/core/rx_service/rx_bus.dart';
import 'package:base_app/core/rx_service/rx_events.dart';
import 'package:base_app/core/translation/app_string.dart';
import 'package:base_app/core/utils/token_decoder_util.dart';
import 'package:base_app/core/widgets/app_base.dart';
import 'package:base_app/core/widgets/app_bottom_bar.dart';
import 'package:base_app/features/activity/pages/activity_page.dart';
import 'package:base_app/features/main/cubit/bottom_bar_cubit.dart';
import 'package:base_app/features/profile/profile_page.dart';
import 'package:base_app/features/home/<USER>/home_page.dart';
import 'package:base_app/gen/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:permission_handler/permission_handler.dart';

class MainPage extends StatefulWidget {
  const MainPage({super.key});

  @override
  State<MainPage> createState() => _MainPageState();
}

class _MainPageState extends State<MainPage> with WidgetsBindingObserver {
  final PageController _pageController = PageController(initialPage: 0);

  @override
  void initState() {
    super.initState();
    _registerChangeCurrentTab();
    getMapBoxKey();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    RxBus.destroy(tag: RxBusTag.changeCurrentTab);
    RxBus.destroy(tag: RxBusTag.naviTab);
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  void _registerChangeCurrentTab() {
    RxBus.register<ChangeCurrentTab>(tag: RxBusTag.changeCurrentTab).listen(
      (event) async => _changeTab(event.currentTab),
    );
    RxBus.register<NaviTab>(tag: RxBusTag.naviTab).listen(
      (event) async => _changeTab(0),
    );
  }

  void getMapBoxKey() async {
    Permission.locationWhenInUse.request();
    final mapBoxKeyDecoded =
        await getIt<ILocalStorageService>().getMapBoxKeyDecoded() ?? '';

    if (mapBoxKeyDecoded.isEmpty) {
      getIt<AppCubit>().getDecodeKey();
      return;
    }
    final mapBoxKey = await getIt<ILocalStorageService>().getMapBoxKey() ?? '';
    if (mapBoxKey.isNotEmpty) {
      return;
    }

    String decodedToken = TokenDecoderUtil.decode(mapBoxKeyDecoded);
    debugPrint('decodedToken => $decodedToken');
    getIt<ILocalStorageService>().setMapBoxKey(decodedToken);
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BottomBarCubit, BottomBarState>(
      bloc: getIt<BottomBarCubit>(),
      builder: (context, stateBottomBar) {
        return AppBase(
          showAppBar: false,
          body: BlocConsumer<AppCubit, AppState>(
            bloc: getIt<AppCubit>(),
            listener: (context, state) {
              if (state.decodeKeyStatus == DecodeKeyStatus.success) {
                AppHelper.instance.setAccessToken();
              }
            },
            builder: (context, state) {
              return PopScope(
                canPop: false,
                onPopInvokedWithResult: (didPop, context) async {
                  if (_pageController.page == 0.0) {
                    SystemNavigator.pop();
                  } else {
                    _changeTab(0);
                    RxBus.post(
                      const NaviTab(),
                      tag: RxBusTag.naviTab,
                    );
                  }
                },
                child: PageView(
                  controller: _pageController,
                  physics: const NeverScrollableScrollPhysics(),
                  children: _buildPages(),
                ),
              );
            },
          ),
          bottomNavigationBar: _buildBottomNav(),
        );
      },
    );
  }

  List<Widget> _buildPages() {
    return [
      HomePage(),
      ActivityPage(),
      ProfilePage(),
      Container(),
    ];
  }

  Widget _buildBottomNav() {
    return AppBottomBar(
      items: [
        NavBarItem(
          icon: Assets.icons.home,
          onTap: _changeTab,
          title: AppString.home,
          iconActive: Assets.icons.homePick,
        ),
        NavBarItem(
          icon: Assets.icons.iconClockGray,
          onTap: _changeTab,
          title: AppString.activity,
          iconActive: Assets.icons.iconClockPick,
        ),
        NavBarItem(
          icon: Assets.icons.iconProfileGray,
          onTap: _changeTab,
          title: AppString.profile,
          iconActive: Assets.icons.iconProfile,
        ),
      ],
    );
  }

  void _changeTab(int index) {
    if (index < 0) {
      _pageController.jumpToPage(0);
    } else {
      _pageController.jumpToPage(index);
    }
  }
}
