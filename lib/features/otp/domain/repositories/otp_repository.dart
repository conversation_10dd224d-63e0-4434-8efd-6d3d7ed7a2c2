import 'package:base_app/core/app/data/models/app_response.dart';
import 'package:base_app/core/utils/task_either_util.dart';
import 'package:base_app/features/otp/data/datasource/otp_remote.dart';
import 'package:base_app/features/otp/data/models/verify_otp_request.dart';
import 'package:injectable/injectable.dart';

@LazySingleton()
class OtpRepository implements IOtpRepository {
  final OtpRemote remoteDataSource;

  OtpRepository(this.remoteDataSource);

  @override
  FutureEither<AppResponse<String>> verifyOTP({
    required VerifyOtpRequest request,
  }) {
    return TaskEitherUtils.fromFuture(
      remoteDataSource.verifyOTP(request: request),
    );
  }
}

abstract class IOtpRepository {
  FutureEither<AppResponse<String>> verifyOTP({
    required VerifyOtpRequest request,
  });
}
