import 'dart:async';
import 'dart:io';

import 'package:base_app/core/di/injection.dart';
import 'package:base_app/core/enums/app_enum.dart';
import 'package:base_app/core/local_storage/i_local_storage_service.dart';
import 'package:base_app/core/translation/app_string.dart';
import 'package:base_app/core/utils/string_util.dart';
import 'package:base_app/features/otp/data/models/verify_otp_request.dart';
import 'package:base_app/features/otp/domain/repositories/otp_repository.dart';
import 'package:dio/dio.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';

part 'otp_state.dart';

@Injectable()
class OtpCubit extends Cubit<OtpState> {
  OtpCubit() : super(const OtpState());
  ILocalStorageService localStoreService = getIt<ILocalStorageService>();
  TextEditingController? otpController = TextEditingController();
  OtpRepository otpRepository = getIt<OtpRepository>();
  Timer? _timer;

  void init(String verificationId, int? resendToken) async {
    String phone = await localStoreService.getUserPhone();
    emit(
      state.copyWith(
        currPhone: phone,
        verificationId: verificationId,
        resendToken: resendToken,
      ),
    );
  }

  void startCountdown(int seconds) {
    emit(state.copyWith(remainingSeconds: seconds, canResend: false));

    _timer?.cancel();

    _timer = Timer.periodic(
      const Duration(seconds: 1),
      (timer) {
        if (state.remainingSeconds > 0) {
          emit(state.copyWith(remainingSeconds: state.remainingSeconds - 1));
        } else {
          timer.cancel();
          emit(state.copyWith(remainingSeconds: 0, canResend: true));
        }
      },
    );
  }

  @override
  Future<void> close() {
    _timer?.cancel();
    return super.close();
  }

  Future<void> signInWithPhoneAuthCredential(
      PhoneAuthCredential credential) async {
    try {
      emit(state.copyWith(otpStatus: OtpStatus.loading));
      UserCredential userCredential =
          await FirebaseAuth.instance.signInWithCredential(credential);

      String accessToken = await userCredential.user?.getIdToken() ?? '';
      final response = await otpRepository.verifyOTP(
        request: VerifyOtpRequest(
          phone: state.currPhone?.formatPhoneNumber ?? '',
          idToken: accessToken,
        ),
      );

      response.fold(
        (failure) {
          emit(state.copyWith(
            otpStatus: OtpStatus.failure,
            errorMessage: failure.message.toString(),
          ));
        },
        (respone) {
          final data = respone.data;
          emit(
            state.copyWith(
              otpStatus: OtpStatus.success,
              otpKeyAccess: data,
            ),
          );
        },
      );
    } on FirebaseAuthException catch (e) {
      emit(
        state.copyWith(
          otpStatus: OtpStatus.failure,
          errorMessage: e.code,
        ),
      );
    } on DioException catch (e) {
      emit(
        state.copyWith(
          otpStatus: OtpStatus.failure,
          errorMessage: e.response?.data['message'][0]['msg'].toString() ??
              AppString.unknownError,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          otpStatus: OtpStatus.failure,
          errorMessage: AppString.unknownError,
        ),
      );
    }
  }

  void verifyOTP(String otp) async {
    try {
      emit(state.copyWith(otpStatus: OtpStatus.loading));
      PhoneAuthCredential credential = PhoneAuthProvider.credential(
        verificationId: state.verificationId ?? '',
        smsCode: otp,
      );
      await signInWithPhoneAuthCredential(credential);
    } catch (_) {}
  }

  void resend() async {
    otpController?.text = "";
    emit(state.copyWith(otpStatus: OtpStatus.loading));
    await FirebaseAuth.instance.verifyPhoneNumber(
      phoneNumber: state.currPhone?.formatPhoneNumber,
      forceResendingToken: state.resendToken,
      verificationCompleted: (PhoneAuthCredential credential) async {
        if (Platform.isAndroid) {
          await signInWithPhoneAuthCredential(credential);
        }
      },
      verificationFailed: (FirebaseAuthException e) {
        emit(
          state.copyWith(
            otpStatus: OtpStatus.failure,
            resendOTPStatus: ResendOTPStatus.failure,
            errorMessage: e.code,
          ),
        );
      },
      codeSent: (String verificationId, int? resendToken) {
        emit(
          state.copyWith(
            otpStatus: OtpStatus.initial,
            verificationId: verificationId,
            resendToken: resendToken,
          ),
        );
      },
      codeAutoRetrievalTimeout: (_) {
        emit(state.copyWith(otpStatus: OtpStatus.failure));
      },
    );
  }
}
