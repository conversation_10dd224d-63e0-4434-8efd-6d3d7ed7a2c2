part of 'otp_cubit.dart';

class OtpState {
  const OtpState({
    this.otpStatus = OtpStatus.initial,
    this.resendOTPStatus = ResendOTPStatus.initial, 
    this.verifyOTPStatus = VerifyOTPStatus.initial,
    this.errorMessage,
    this.currPhone,
    this.verificationId,
    this.otpKeyAccess,
    this.resendToken,
    this.remainingSeconds = 0,
    this.canResend = true,
  });
  final OtpStatus otpStatus;
  final ResendOTPStatus resendOTPStatus;
  final VerifyOTPStatus verifyOTPStatus;
  final String? errorMessage;
  final String? currPhone;
  final String? verificationId;
  final String? otpKeyAccess;
  final int? resendToken;
  final int remainingSeconds;
  final bool canResend;

  OtpState copyWith({
    OtpStatus? otpStatus,
    ResendOTPStatus? resendOTPStatus,
    VerifyOTPStatus? verifyOTPStatus,
    String? errorMessage,
    String? currPhone,
    String? verificationId,
    String? otpKeyAccess,
    int? resendToken,
    int? remainingSeconds,
    bool? canResend,
  }) {
    return OtpState(
      otpStatus: otpStatus ?? this.otpStatus,
      resendOTPStatus: resendOTPStatus ?? this.resendOTPStatus,
      verifyOTPStatus: verifyOTPStatus ?? this.verifyOTPStatus,
      errorMessage: errorMessage ?? this.errorMessage,
      currPhone: currPhone ?? this.currPhone,
      verificationId: verificationId ?? this.verificationId,
      otpKeyAccess: otpKeyAccess ?? this.otpKeyAccess,
      resendToken: resendToken ?? this.resendToken,
      remainingSeconds: remainingSeconds ?? this.remainingSeconds,
      canResend: canResend ?? this.canResend,
    );
  }
}