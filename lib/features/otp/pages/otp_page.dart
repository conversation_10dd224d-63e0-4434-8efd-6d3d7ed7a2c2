import 'package:base_app/core/di/injection.dart';
import 'package:base_app/core/enums/app_enum.dart';
import 'package:base_app/core/enums/firebase_error_code.dart';
import 'package:base_app/core/extension/app_extension_context.dart';
import 'package:base_app/core/theme/app_colors.dart';
import 'package:base_app/core/theme/app_text_styles.dart';
import 'package:base_app/core/translation/app_string.dart';
import 'package:base_app/core/utils/loading_overlay_util.dart';
import 'package:base_app/core/utils/safe_navigation_util.dart';
import 'package:base_app/core/widgets/app_base.dart';
import 'package:base_app/core/widgets/app_snack_bar.dart';
import 'package:base_app/features/otp/cubit/otp_cubit.dart';
import 'package:base_app/router/app_router.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pinput/pinput.dart';

class OtpPage extends StatefulWidget {
  const OtpPage({
    super.key,
    required this.verificationId,
    this.resendToken,
    this.userName,
  });
  final String verificationId;
  final int? resendToken;
  final String? userName;

  @override
  State<OtpPage> createState() => _SignUpScreenState();
}

class _SignUpScreenState extends State<OtpPage> {
  OtpCubit otpCubit = getIt<OtpCubit>();

  @override
  void initState() {
    super.initState();
    otpCubit.init(widget.verificationId, widget.resendToken);
  }

  @override
  Widget build(BuildContext context) {
    final defaultPinTheme = PinTheme(
      width: 40,
      height: 56,
      textStyle: AppTextStyle.regular(14.sp),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.brightGray),
        borderRadius: BorderRadius.circular(5.r),
      ),
    );

    final focusedPinTheme = defaultPinTheme.copyDecorationWith(
      border: Border.all(color: AppColors.honoluluBlue),
      borderRadius: BorderRadius.circular(5.r),
    );

    final submittedPinTheme = defaultPinTheme.copyWith(
      decoration: defaultPinTheme.decoration!.copyWith(
        color: AppColors.brightGray,
      ),
    );

    return BlocListener<OtpCubit, OtpState>(
      bloc: otpCubit,
      listener: _listener,
      child: AppBase(
        titleAppBar: AppString.verifyOTP,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              BlocBuilder<OtpCubit, OtpState>(
                bloc: otpCubit,
                builder: (context, state) {
                  return RichText(
                    text: TextSpan(
                      text: AppString.verifyOTPSendToPhone,
                      style: AppTextStyle.regular(14.sp),
                      children: [
                        TextSpan(
                          text: state.currPhone,
                          style: AppTextStyle.bold(14.sp).copyWith(
                            color: AppColors.vampireBlack,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
              context.verticalSpaceLarge,
              Pinput(
                length: 6,
                controller: otpCubit.otpController,
                defaultPinTheme: defaultPinTheme,
                focusedPinTheme: focusedPinTheme,
                submittedPinTheme: submittedPinTheme,
                pinputAutovalidateMode: PinputAutovalidateMode.onSubmit,
                showCursor: true,
                onCompleted: (pin) {
                  SafeNavigationUtil.go(context, AppRouter.signup,
                      extra: {
                        'userName': widget.userName,
                      },
                    );
                  //otpCubit.verifyOTP(pin);
                },
              ),
              context.verticalSpaceVeryHigh,
              BlocBuilder<OtpCubit, OtpState>(
                bloc: otpCubit,
                builder: (context, state) {
                  return RichText(
                    text: TextSpan(
                      text: "${AppString.didNotReceiveOTP} ",
                      style: AppTextStyle.regular(12.sp).copyWith(
                        color: AppColors.taupeGray,
                      ),
                      children: [
                        if (state.remainingSeconds > 0) ...[
                          TextSpan(
                            text:
                                AppString.resendSeconds(state.remainingSeconds),
                            style: AppTextStyle.regular(14.sp).copyWith(
                              color: AppColors.honoluluBlue,
                            ),
                          )
                        ] else ...[
                          TextSpan(
                            recognizer: TapGestureRecognizer()
                              ..onTap = state.canResend
                                  ? () {
                                      otpCubit.startCountdown(60);
                                      otpCubit.resend();
                                    }
                                  : () {},
                            text: AppString.resendOTP,
                            style: AppTextStyle.bold(14.sp).copyWith(
                              color: AppColors.red,
                            ),
                          ),
                        ],
                      ],
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _listener(BuildContext context, OtpState state) {
    if (state.otpStatus == OtpStatus.loading) {
      AppLoadingOverlay.show(context);
    } else if (state.otpStatus == OtpStatus.success) {
      AppLoadingOverlay.hide();
    } else if (state.otpStatus == OtpStatus.failure ||
        state.resendOTPStatus == ResendOTPStatus.failure) {
      AppLoadingOverlay.hide();
      final errorCode = FirebaseErrorCode.fromCode(state.errorMessage);
      String error = switch (errorCode) {
        FirebaseErrorCode.invalidVer => AppString.invalidOtpCode,
        FirebaseErrorCode.sessionExpired => AppString.sessionExpired,
        FirebaseErrorCode.invalidVerId => AppString.invalidVerificationId,
        FirebaseErrorCode.networkRequestFailed => AppString.networkError,
        _ => AppString.unknownError,
      };
      AppSnackBar.error(
        context,
        message: error,
      );
    }
  }
}
