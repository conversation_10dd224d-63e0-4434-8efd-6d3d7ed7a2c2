
import 'package:freezed_annotation/freezed_annotation.dart';

part 'verify_otp_request.g.dart';

@JsonSerializable(explicitToJson: true)
class VerifyOtpRequest {
  final String phone;
  final String idToken;

  VerifyOtpRequest({
    required this.phone,
    required this.idToken,
  });

  factory VerifyOtpRequest.fromJson(Map<String, dynamic> json) =>
      _$VerifyOtpRequestFromJson(json);

  Map<String, dynamic> toJson() => _$VerifyOtpRequestToJson(this);
}