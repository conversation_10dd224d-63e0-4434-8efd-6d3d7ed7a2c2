

import 'package:base_app/core/app/data/models/app_response.dart';
import 'package:base_app/core/constants/api_constants.dart';
import 'package:base_app/features/otp/data/models/verify_otp_request.dart';
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:retrofit/retrofit.dart';

part 'otp_remote.g.dart';

@RestApi()
@LazySingleton()
abstract class OtpRemote {
  @factoryMethod
  factory OtpRemote(Dio dio) = _OtpRemote;

  @POST(ApiConstants.verifyOTP)
  Future<AppResponse<String>> verifyOTP({
    @Body() required VerifyOtpRequest request,
  });
}
