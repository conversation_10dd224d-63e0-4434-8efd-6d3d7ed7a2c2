import 'package:base_app/core/di/injection.dart';
import 'package:base_app/core/extension/app_extension_context.dart';
import 'package:base_app/core/rx_service/rx_bus.dart';
import 'package:base_app/core/rx_service/rx_events.dart';
import 'package:base_app/core/theme/app_text_styles.dart';
import 'package:base_app/core/utils/safe_navigation_util.dart';
import 'package:base_app/core/widgets/app_base.dart';
import 'package:base_app/core/widgets/app_button.dart';
import 'package:base_app/features/login/cubit/auth_cubit.dart';
import 'package:base_app/core/config/flavor_config.dart';
import 'package:base_app/router/app_router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  @override
  Widget build(BuildContext context) {
    return AppBase(
      showBackButton: false,
      titleAppBar: 'Profile',
      body: BlocConsumer<AuthCubit, AuthState>(
        bloc: getIt<AuthCubit>(),
        listener: (context, state) {
          if (state.logoutStatus == LogoutStatus.success) {
            RxBus.post(const NaviTab(), tag: RxBusTag.naviTab);
            SafeNavigationUtil.go(context, AppRouter.login);
          }
        },
        builder: (context, state) {
          return Padding(
            padding: EdgeInsets.symmetric(horizontal: context.valuePaddingHigh),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  context.verticalSpaceHigh,
                  Text(
                    "Environment: ${FlavorConfig.appEnv}",
                    style: AppTextStyle.medium(),
                  ),
                  context.verticalSpaceHigh,
                  AppButton(
                    title: 'Logout',
                    onTap: () {
                      getIt<AuthCubit>().logout();
                    },
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
