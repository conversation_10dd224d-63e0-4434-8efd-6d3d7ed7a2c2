import 'package:base_app/core/theme/app_colors.dart';
import 'package:base_app/core/theme/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BuildTimeWidget extends StatelessWidget {
  final Color? colorText;
  const BuildTimeWidget({
    super.key,
    this.colorText,
  });

  @override
  Widget build(BuildContext context) {
    return Text(
      '1/10/2025 • 17:30',
      style: AppTextStyle.regular(14.sp).copyWith(
        color: colorText ?? AppColors.philippineGray,
      ),
    );
  }
}
