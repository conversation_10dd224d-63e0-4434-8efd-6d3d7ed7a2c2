import 'package:base_app/core/extension/app_extension_context.dart';
import 'package:base_app/core/theme/app_text_styles.dart';
import 'package:base_app/core/widgets/app_with_dot_center.dart';
import 'package:base_app/gen/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'build_status_widget.dart';
import 'build_time_widget.dart';

class BuildHeaderWidget extends StatelessWidget {
  const BuildHeaderWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            Image.asset(
              Assets.icons.iconLogo.path,
              width: 32.dm,
              height: 32.dm,
              fit: BoxFit.cover,
            ),
            context.horizontalSpaceNormal,
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                BuildTimeWidget(),
                context.verticalSpaceSmall,
                AppWithDotCenter(
                  contentLeft: '9999 vnđ',
                  contentRight: 'loại xe',
                  styleLeft: AppTextStyle.bold(),
                  styleRight: AppTextStyle.bold(),
                ),
              ],
            ),
          ],
        ),
        BuildStatusWidget(),
      ],
    );
  }
}
