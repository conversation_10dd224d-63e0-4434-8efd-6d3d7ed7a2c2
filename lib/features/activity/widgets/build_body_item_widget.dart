import 'package:base_app/core/extension/app_extension_context.dart';
import 'package:base_app/core/widgets/app_vertical_divider.dart';
import 'package:base_app/features/activity/widgets/build_icon_text_widget.dart';
import 'package:base_app/gen/assets.gen.dart';
import 'package:flutter/material.dart';

class BuildBodyItemWidget extends StatelessWidget {
  const BuildBodyItemWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        BuildIconTextWidget(
          pathIcon: Assets.icons.iconDirectUp.path,
          text: "<PERSON><PERSON><PERSON> điểm đón",
          subText: "123456789",
        ),
        context.verticalSpaceSmall,
        AppVerticalDivider(
          margin: EdgeInsets.only(
            left: context.valuePaddingMedium,
          ),
        ),
        context.verticalSpaceSmall,
        BuildIconTextWidget(
          pathIcon: Assets.icons.iconDestination.path,
          text: "<PERSON><PERSON><PERSON><PERSON> đến",
          subText: "123456789",
        ),
      ],
    );
  }
}