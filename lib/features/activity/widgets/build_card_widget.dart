import 'package:base_app/core/extension/app_extension_context.dart';
import 'package:base_app/core/theme/app_colors.dart';
import 'package:base_app/core/widgets/app_animated_button.dart';
import 'package:flutter/material.dart';

class BuildCardWidget extends StatelessWidget {
  final Widget child;
  final double? height;
  final void Function()? onTap;
  const BuildCardWidget({
    super.key,
    required this.child,
    this.height,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return AppAnimatedButton(
      onTap: onTap,
      child: Container(
        height: height,
        padding: EdgeInsets.all(
          context.valuePaddingHigh,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppColors.gray400,
          ),
        ),
        child: child,
      ),
    );
  }
}
