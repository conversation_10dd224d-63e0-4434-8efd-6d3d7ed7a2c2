import 'package:base_app/core/extension/app_extension_context.dart';
import 'package:base_app/core/theme/app_colors.dart';
import 'package:base_app/core/theme/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BuildIconTextWidget extends StatelessWidget {
  final String pathIcon;
  final String text;
  final String? subText;
  const BuildIconTextWidget({
    super.key,
    required this.pathIcon,
    required this.text,
    this.subText
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Image.asset(
          pathIcon,
          width: 24.dm,
          height: 24.dm,
          fit: BoxFit.cover,
        ),
        context.horizontalSpaceNormal,
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              text,
              style: AppTextStyle.medium(14.sp),
            ),
            context.verticalSpaceSmall,
            if (subText?.isNotEmpty == true)
              Text(
                subText!,
                style: AppTextStyle.regular(12.sp).copyWith(
                  color: AppColors.philippineGray,
                ),
              ),
          ],
        ),
      ],
    );
  }
}
