import 'package:base_app/core/extension/app_extension_context.dart';
import 'package:base_app/core/theme/app_colors.dart';
import 'package:base_app/core/theme/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BuildPlateWidget extends StatelessWidget {
  const BuildPlateWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: context.valuePaddingHigh,
        vertical: context.valuePaddingNormal,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.r),
        color: AppColors.cgBlue,
      ),
      child: Text(
        "30E-999.99",
        style: AppTextStyle.bold().copyWith(
          color: AppColors.white,
        ),
      ),
    );
  }
}
