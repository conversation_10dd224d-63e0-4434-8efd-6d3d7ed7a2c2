import 'package:base_app/core/extension/app_extension_context.dart';
import 'package:base_app/core/widgets/app_overlay_dropdown.dart';
import 'package:flutter/material.dart';

class OnGoingWidget extends StatefulWidget {
  const OnGoingWidget({super.key});

  @override
  State<OnGoingWidget> createState() => _OnGoingWidgetState();
}

class _OnGoingWidgetState extends State<OnGoingWidget> {
  final items = [
    DropdownItem(name: 'Apple'),
    DropdownItem(name: 'Banana'),
    DropdownItem(name: 'Mango'),
    DropdownItem(name: 'Orange'),
  ];
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: context.pdHighHorizontal,
        child: AppOverlayDropdown(
          items: items,
          hintText: 'Select Fruit',
          onChanged: (value) {
            debugPrint('Selected value: ${value.name}');
          },
        ),
      ),
    );
  }
}
