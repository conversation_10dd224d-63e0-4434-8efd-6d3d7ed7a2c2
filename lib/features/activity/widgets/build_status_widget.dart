import 'package:base_app/core/extension/app_extension_context.dart';
import 'package:base_app/core/theme/app_colors.dart';
import 'package:base_app/core/theme/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BuildStatusWidget extends StatelessWidget {
  const BuildStatusWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: context.valuePaddingHigh,
        vertical: context.valuePaddingNormal,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: AppColors.darkMantis,
      ),
      child: Text(
        'Đã thanh toán',
        style: AppTextStyle.medium(14.sp).copyWith(
          color: AppColors.white,
        ),
      ),
    );
  }
}
