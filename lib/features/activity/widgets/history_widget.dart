import 'package:base_app/core/extension/app_extension_context.dart';
import 'package:base_app/features/activity/widgets/build_chip_widget.dart';
import 'package:flutter/material.dart';

import 'build_item_widget.dart';

class HistoryWidget extends StatelessWidget {
  const HistoryWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: context.valuePaddingHigh),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            height: context.highValue,
            child: ListView.separated(
              padding: EdgeInsets.zero,
              scrollDirection: Axis.horizontal,
              separatorBuilder: (_, __) => context.horizontalSpaceMedium,
              itemCount: 3,
              itemBuilder: (context, index) {
                return BuildChipWidget(
                  index: index,
                  label: 'Type $index',
                  isSelected: index == 0,
                  onTap: (index) {},
                );
              },
            ),
          ),
          context.verticalSpaceMedium,
          BuildItemWidget()
        ],
      ),
    );
  }
}
