import 'package:base_app/core/theme/app_colors.dart';
import 'package:base_app/core/theme/app_text_styles.dart';
import 'package:base_app/core/widgets/app_animated_button.dart';
import 'package:flutter/material.dart';

class BuildBookAgainWidget extends StatelessWidget {
  final void Function()? onPressed;
  const BuildBookAgainWidget({
    super.key,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return AppAnimatedButton(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Đặt lại lần nữa',
            style: AppTextStyle.bold(14).copyWith(
              color: AppColors.honoluluBlue,
            ),
          ),
          Icon(
            Icons.arrow_forward_ios,
            size: 16,
            color: AppColors.honoluluBlue,
          ),
        ],
      ),
    );
  }
}
