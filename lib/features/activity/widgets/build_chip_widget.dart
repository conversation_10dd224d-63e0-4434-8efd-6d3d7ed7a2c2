import 'package:base_app/core/theme/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BuildChipWidget extends StatelessWidget {
  final String label;
  final bool isSelected;
  final Function(int)? onTap;
  final int index;
  const BuildChipWidget({
    super.key,
    required this.index,
    required this.label,
    this.isSelected = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ActionChip(
      onPressed: () => onTap?.call(index),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      label: Text(
        label,
        style: TextStyle(
          color: isSelected ? AppColors.white : AppColors.darkCharcoal,
        ),
      ),
      backgroundColor: isSelected ? AppColors.honoluluBlue : AppColors.gray,
      side: BorderSide(
        color: isSelected ? AppColors.honoluluBlue : AppColors.gray,
        width: 1,
      ),
    );
  }
}
