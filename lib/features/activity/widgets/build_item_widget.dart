import 'package:base_app/core/extension/app_extension_context.dart';
import 'package:base_app/core/utils/safe_navigation_util.dart';
import 'package:base_app/core/widgets/app_dotted_line.dart';
import 'package:base_app/features/activity/widgets/build_card_widget.dart';
import 'package:base_app/router/app_router.dart';
import 'package:flutter/material.dart';

import 'build_body_item_widget.dart';
import 'build_header_item_widget.dart';
import 'build_text_button.dart';

class BuildItemWidget extends StatelessWidget {
  const BuildItemWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BuildCardWidget(
      onTap: () {
        SafeNavigationUtil.push(context, AppRouter.detail);
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          BuildHeaderWidget(),
          context.verticalSpaceHigh,
          AppDottedLine(),
          context.verticalSpaceHigh,
          BuildBodyItemWidget(),
          context.verticalSpaceHigh,
          AppDottedLine(),
          context.verticalSpaceHigh,
          BuildBookAgainWidget(),
        ],
      ),
    );
  }
}
