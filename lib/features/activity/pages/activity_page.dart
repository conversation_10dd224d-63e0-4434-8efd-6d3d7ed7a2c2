import 'package:base_app/core/theme/app_colors.dart';
import 'package:base_app/core/theme/app_text_styles.dart';
import 'package:base_app/core/translation/app_string.dart';
import 'package:base_app/core/widgets/app_base.dart';
import 'package:base_app/core/widgets/app_tab_bar.dart';
import 'package:base_app/features/activity/widgets/history_widget.dart';
import 'package:base_app/features/activity/widgets/on_going_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ActivityPage extends StatefulWidget {
  const ActivityPage({super.key});

  @override
  State<ActivityPage> createState() => _ActivityPageState();
}

class _ActivityPageState extends State<ActivityPage> {
  @override
  Widget build(BuildContext context) {
    return AppBase(
      centerTitle: false,
      leading: SizedBox.shrink(),
      backgroundColorAppBar: AppColors.white,
      titleStyle: AppTextStyle.bold(24.sp).copyWith(
        color: AppColors.black,
      ),
      leadingWidth: 8,
      titleAppBar: AppString.activity,
      body: AppTabBar(
        tabs: [
          AppString.onGoing,
          AppString.history,
        ],
        tabContents: [
          OnGoingWidget(),
          HistoryWidget(),
        ],
      ),
    );
  }
}
