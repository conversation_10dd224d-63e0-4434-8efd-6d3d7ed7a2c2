import 'package:base_app/core/extension/app_extension_context.dart';
import 'package:base_app/core/theme/app_colors.dart';
import 'package:base_app/core/theme/app_text_styles.dart';
import 'package:base_app/core/translation/app_string.dart';
import 'package:base_app/core/widgets/app_animated_button.dart';
import 'package:base_app/core/widgets/app_base.dart';
import 'package:base_app/core/widgets/app_button.dart';
import 'package:base_app/core/widgets/app_copy.dart';
import 'package:base_app/core/widgets/app_with_dot_center.dart';
import 'package:base_app/features/activity/widgets/build_body_item_widget.dart';
import 'package:base_app/features/activity/widgets/build_card_widget.dart';
import 'package:base_app/features/activity/widgets/build_plate_widget.dart';
import 'package:base_app/features/activity/widgets/build_status_widget.dart';
import 'package:base_app/features/activity/widgets/build_time_widget.dart';
import 'package:base_app/gen/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class DetailPage extends StatelessWidget {
  const DetailPage({super.key});

  @override
  Widget build(BuildContext context) {
    return AppBase(
      titleAppBar: AppString.detail,
      actions: [
        AppAnimatedButton(
          onTap: () {},
          child: Assets.icons.iconTrash.image(
            width: 24.dm,
            height: 24.dm,
            color: AppColors.white,
            fit: BoxFit.cover,
          ),
        ),
        context.horizontalSpaceHigh,
      ],
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: context.valuePaddingHigh),
          child: Column(
            children: [
              context.verticalSpaceHigh,
              BuildCardWidget(
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        BuildTimeWidget(
                          colorText: AppColors.black,
                        ),
                        BuildStatusWidget()
                      ],
                    ),
                    context.verticalSpaceNormal,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          AppString.orderNumber,
                          style: AppTextStyle.regular(14.sp),
                        ),
                        AppCopy(
                          text: '123456789',
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              context.verticalSpaceHigh,
              BuildCardWidget(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Assets.icons.iconLogo.image(
                          width: 32.dm,
                          height: 32.dm,
                          fit: BoxFit.cover,
                        ),
                        context.verticalSpaceSmall,
                        Row(
                          children: [
                            AppWithDotCenter(
                              contentLeft: 'Nguyễn Văn A',
                              contentRight: '5',
                            ),
                            Icon(
                              Icons.star,
                              color: Colors.yellow,
                            )
                          ],
                        ),
                      ],
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        BuildPlateWidget(),
                        context.verticalSpaceSmall,
                        AppWithDotCenter(
                          contentLeft: "Hãng xe",
                          contentRight: "Màu",
                        ),
                      ],
                    )
                  ],
                ),
              ),
              context.verticalSpaceHigh,
              BuildCardWidget(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "Tên xe",
                          style: AppTextStyle.medium(),
                        ),
                        AppCopy(
                          text: '123456789',
                        ),
                      ],
                    ),
                    context.verticalSpaceNormal,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "Thời gian",
                          style: AppTextStyle.medium(),
                        ),
                        BuildTimeWidget(
                          colorText: AppColors.black,
                        ),
                      ],
                    ),
                    context.verticalSpaceNormal,
                    BuildBodyItemWidget(),
                  ],
                ),
              ),
              context.verticalSpaceLarge,
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: AppButton(
                      title: AppString.cancel,
                      type: ButtonType.outline,
                    ),
                  ),
                  context.horizontalSpaceHigh,
                  Expanded(
                    child: AppButton(
                      onTap: () {},
                      title: AppString.confirm,
                      type: ButtonType.primary,
                    ),
                  ),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }
}
