
import 'package:base_app/core/app/data/models/app_response.dart';
import 'package:base_app/core/constants/api_constants.dart';
import 'package:base_app/features/signup/data/models/signup_request.dart';
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:retrofit/retrofit.dart';

part 'signup_remote.g.dart';

@RestApi()
@LazySingleton()
abstract class SignupRemote {
  @factoryMethod
  factory SignupRemote(Dio dio) = _SignupRemote;

  @POST(ApiConstants.customerRegister)
  Future<AppResponse<String>> customerRegister({
    @Body() required SignupRequest request,
  });
}