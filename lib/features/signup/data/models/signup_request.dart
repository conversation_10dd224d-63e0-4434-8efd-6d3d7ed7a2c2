
import 'package:freezed_annotation/freezed_annotation.dart';

part 'signup_request.g.dart';

@JsonSerializable(explicitToJson: true)
class SignupRequest {
  final String username;
  final String password;
  final String fullName;
  final String email;

  SignupRequest({
    required this.username,
    required this.password,
    required this.fullName,
    required this.email,
  });

  factory SignupRequest.fromJson(Map<String, dynamic> json) =>
      _$SignupRequestFromJson(json);

  Map<String, dynamic> toJson() => _$SignupRequestToJson(this);
}