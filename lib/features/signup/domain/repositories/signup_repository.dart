

import 'package:base_app/core/app/data/models/app_response.dart';
import 'package:base_app/core/utils/task_either_util.dart';
import 'package:base_app/features/signup/data/datasource/signup_remote.dart';
import 'package:base_app/features/signup/data/models/signup_request.dart';
import 'package:injectable/injectable.dart';


@LazySingleton()
class SignupRepository implements ISignupRepository {
  final SignupRemote remoteDataSource;

  SignupRepository(this.remoteDataSource);

  @override
  FutureEither<AppResponse<String>> customerRegister({
    required SignupRequest request,
  }) {
    return TaskEitherUtils.fromFuture(
      remoteDataSource.customerRegister(request: request),
    );
  }
}

abstract class ISignupRepository {
  FutureEither<AppResponse<String>> customerRegister({
    required SignupRequest request,
  });
}
