import 'package:base_app/core/di/injection.dart';
import 'package:base_app/core/enums/app_enum.dart';
import 'package:base_app/core/extension/app_extension_context.dart';
import 'package:base_app/core/translation/app_string.dart';
import 'package:base_app/core/utils/loading_overlay_util.dart';
import 'package:base_app/core/utils/validate_util.dart';
import 'package:base_app/core/widgets/app_base.dart';
import 'package:base_app/core/widgets/app_button.dart';
import 'package:base_app/core/widgets/app_input.dart';
import 'package:base_app/core/widgets/app_snack_bar.dart';
import 'package:base_app/features/signup/bloc/signup_bloc.dart';
import 'package:base_app/features/signup/data/models/signup_request.dart';
import 'package:base_app/gen/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SignupPage extends StatefulWidget {
  final String? otpKey;
  final String? userName;
  const SignupPage({
    super.key,
    this.otpKey,
    required this.userName,
  });

  @override
  State<SignupPage> createState() => _SignupPageState();
}

class _SignupPageState extends State<SignupPage> {
  final _formKey = GlobalKey<FormState>();
  final _phoneCtrl = TextEditingController();
  final _passwordCtrl = TextEditingController();
  final _confirmPasswordCtrl = TextEditingController();
  final _emailCtrl = TextEditingController();
  final _nameCtrl = TextEditingController();
  final _bloc = getIt<SignupBloc>();

  @override
  void dispose() {
    _phoneCtrl.dispose();
    _passwordCtrl.dispose();
    _confirmPasswordCtrl.dispose();
    _emailCtrl.dispose();
    _nameCtrl.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AppBase(
      titleAppBar: AppString.userRegistration,
      paddingBody: context.paddingBody,
      body: SingleChildScrollView(
        child: BlocConsumer<SignupBloc, SignupState>(
          bloc: _bloc,
          listener: (context, state) {
            if (state.signupStatus == SignupStatus.loading) {
              AppLoadingOverlay.show(context);
            } else if (state.signupStatus == SignupStatus.success) {
              AppLoadingOverlay.hide();
              AppSnackBar.success(
                context,
                message: state.message ?? '',
              );
              _bloc.add(ResetState());
            } else if (state.signupStatus == SignupStatus.failure) {
              AppLoadingOverlay.hide();
              AppSnackBar.error(
                context,
                message: state.errorMessage ?? '',
              );
              _bloc.add(ResetState());
            }
          },
          builder: (context, state) {
            return Form(
              key: _formKey,
              child: Column(
                children: [
                  context.verticalSpaceLarge,
                  AppTextFormField(
                    controller: _nameCtrl,
                    submitted: state.submitted,
                    label: AppString.fullName,
                    hintText: AppString.enterFullName,
                    isRequired: true,
                    validator: (value) => ValidateUtil.instance.requiredField(
                      value: value,
                      isRequired: true,
                      type: ValidationType.fullName,
                    ),
                  ),
                  context.verticalSpaceHigh,
                  AppTextFormField(
                    controller: _phoneCtrl,
                    submitted: state.submitted,
                    label: AppString.phoneNumber,
                    hintText: AppString.enterPhoneNumber,
                    keyboardType: TextInputType.number,
                    isRequired: true,
                    validator: (value) => ValidateUtil.instance.requiredField(
                      value: value,
                      isRequired: true,
                      type: ValidationType.phone,
                    ),
                  ),
                  context.verticalSpaceHigh,
                  AppTextFormField(
                    controller: _passwordCtrl,
                    submitted: state.submitted,
                    label: AppString.password,
                    hintText: AppString.enterPassword,
                    isRequired: true,
                    obscureText: state.obscureText,
                    suffixIcon: GestureDetector(
                      onTap: () =>
                          _bloc.add(ShowOrHidePassword(state.obscureText)),
                      child: Padding(
                        padding: EdgeInsets.all(context.valuePaddingNormal),
                        child: state.obscureText
                            ? Assets.svgs.hide.svg(height: 24.dm)
                            : Assets.svgs.view.svg(height: 24.dm),
                      ),
                    ),
                    validator: (value) => ValidateUtil.instance.requiredField(
                      value: value,
                      isRequired: true,
                      type: ValidationType.password,
                    ),
                  ),
                  context.verticalSpaceHigh,
                  AppTextFormField(
                    controller: _confirmPasswordCtrl,
                    submitted: state.submitted,
                    label: AppString.confirmPassword,
                    hintText: AppString.enterConfirmPassword,
                    isRequired: true,
                    obscureText: state.obscureTextConfirm,
                    suffixIcon: GestureDetector(
                      onTap: () => _bloc.add(
                          ShowOrHidePasswordConfirm(state.obscureTextConfirm)),
                      child: Padding(
                        padding: EdgeInsets.all(context.valuePaddingNormal),
                        child: state.obscureTextConfirm
                            ? Assets.svgs.hide.svg(height: 24.dm)
                            : Assets.svgs.view.svg(height: 24.dm),
                      ),
                    ),
                    validator: (value) => ValidateUtil.instance.requiredField(
                      value: value,
                      isRequired: true,
                      type: ValidationType.confirmPassword,
                      passwordConfirm: _passwordCtrl.text,
                    ),
                  ),
                  context.verticalSpaceHigh,
                  AppTextFormField(
                    submitted: state.submitted,
                    label: AppString.email,
                    hintText: AppString.enterEmail,
                    isRequired: false,
                  ),
                  context.verticalSpaceLarge,
                  AppButton(
                    title: AppString.register,
                    onTap: () {
                      _signUp();
                    },
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  void _signUp() {
    _bloc.add(SetSubmitted(true));
    final bool isValid = _formKey.currentState?.validate() == true;
    debugPrint('isValid: $isValid');
    if (isValid) {
      _bloc.add(
        Submitted(
          SignupRequest(
            fullName: _nameCtrl.text,
            username: _phoneCtrl.text,
            password: _passwordCtrl.text,
            email: _emailCtrl.text,
          ),
        ),
      );
    }
  }
}
