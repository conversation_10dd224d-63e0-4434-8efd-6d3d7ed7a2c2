part of 'signup_bloc.dart';

abstract class SignupEvent {}

class Submitted extends SignupEvent {
  final SignupRequest request;
  Submitted(this.request);
}

class ShowOrHidePassword extends SignupEvent {
  final bool obscureText;
  ShowOrHidePassword(this.obscureText);
}

class SetSubmitted extends SignupEvent {
  final bool submitted;
  SetSubmitted(this.submitted);
}

class ShowOrHidePasswordConfirm extends SignupEvent {
  final bool obscureText;
  ShowOrHidePasswordConfirm(this.obscureText);
}

class ResetState extends SignupEvent {}
