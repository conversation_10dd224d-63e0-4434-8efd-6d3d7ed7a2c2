
part of 'signup_bloc.dart';



class SignupState {
  final SignupStatus signupStatus;
  final String? errorMessage;
  final String? message;
  final bool obscureText;
  final bool obscureTextConfirm;
  final bool submitted;

  const SignupState({
    this.signupStatus = SignupStatus.initial,
    this.errorMessage,
    this.message,
    this.obscureText = true,
    this.submitted = false,
    this.obscureTextConfirm = true,
  });

  SignupState copyWith({
    SignupStatus? signupStatus,
    String? errorMessage,
    String? message,
    bool? obscureText,
    bool? obscureTextConfirm,
    bool? submitted,
  }) {
    return SignupState(
      signupStatus: signupStatus ?? this.signupStatus,
      errorMessage: errorMessage ?? this.errorMessage,
      message: message ?? this.message,
      obscureText: obscureText ?? this.obscureText,
      submitted: submitted ?? this.submitted,
      obscureTextConfirm: obscureTextConfirm ?? this.obscureTextConfirm,
    );
  }
}