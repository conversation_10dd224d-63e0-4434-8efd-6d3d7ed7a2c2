import 'package:base_app/core/di/injection.dart';
import 'package:base_app/core/enums/app_enum.dart';
import 'package:base_app/features/signup/data/models/signup_request.dart';
import 'package:base_app/features/signup/domain/repositories/signup_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';

part 'signup_state.dart';
part 'signup_event.dart';

@Injectable()
class SignupBloc extends Bloc<SignupEvent, SignupState> {
  SignupRepository repository = getIt<SignupRepository>();
  SignupBloc() : super(const SignupState()) {
    on<Submitted>(_onSubmitted);
    on<ShowOrHidePassword>(_onShowOrHidePassword);
    on<SetSubmitted>(_onSetSubmitted);
    on<ShowOrHidePasswordConfirm>(_onShowOrHidePasswordConfirm);
    on<ResetState>(_onResetState);
  }

  Future<void> _onSubmitted(Submitted event, Emitter<SignupState> emit) async {
    emit(state.copyWith(signupStatus: SignupStatus.loading));
    final response = await repository.customerRegister(request: event.request);

    response.fold(
      (failure) {
        emit(
          state.copyWith(
            signupStatus: SignupStatus.failure,
            errorMessage: failure.message.toString(),
          ),
        );
      },
      (response) {
        final message = response.data;
        emit(
          state.copyWith(
            signupStatus: SignupStatus.success,
            message: message,
          ),
        );
      },
    );
  }

  void _onShowOrHidePassword(
      ShowOrHidePassword event, Emitter<SignupState> emit) {
    emit(state.copyWith(obscureText: !event.obscureText));
  }

  void _onSetSubmitted(SetSubmitted event, Emitter<SignupState> emit) {
    emit(state.copyWith(submitted: event.submitted));
  }

  void _onShowOrHidePasswordConfirm(
      ShowOrHidePasswordConfirm event, Emitter<SignupState> emit) {
    emit(state.copyWith(obscureTextConfirm: !event.obscureText));
  }

  void _onResetState(ResetState event, Emitter<SignupState> emit) {
    emit(
      state.copyWith(
        signupStatus: SignupStatus.initial,
        errorMessage: null,
        message: null,
        submitted: false,
      ),
    );
  }
}
