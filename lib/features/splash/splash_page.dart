import 'package:base_app/core/di/injection.dart';
import 'package:base_app/core/helper/app_hepler.dart';
import 'package:base_app/core/utils/safe_navigation_util.dart';
import 'package:base_app/core/widgets/app_base.dart';
import 'package:base_app/features/login/cubit/auth_cubit.dart';
import 'package:base_app/router/app_router.dart';
import 'package:flutter/material.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animScale;
  late Animation<double> _opacityAnim;
  late AuthCubit? authCubit;

  @override
  void initState() {
    super.initState();

    _initAnimation();
    authCubit = getIt<AuthCubit>();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      _animationController.forward();
      AppHelper.instance.setAccessToken();
    });
  }

  void _initAnimation() {
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(
        milliseconds: 1000,
      ),
    );
    _animScale = Tween(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.ease,
      ),
    );
    _opacityAnim = Tween(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.ease,
      ),
    );

    _animationController.addStatusListener(
      (status) async {
        if (status == AnimationStatus.completed) {
          await Future.delayed(
            const Duration(
              milliseconds: 500,
            ),
          );
          _navigateToMainApp();
        }
      },
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _navigateToMainApp() {
    if (!mounted) return;

    authCubit?.state.status == AuthenticationStatus.authenticated
        ? SafeNavigationUtil.go(context, AppRouter.main)
        : SafeNavigationUtil.go(context, AppRouter.login);
  }

  @override
  Widget build(BuildContext context) {
    return AppBase(
      showAppBar: false,
      body: FadeTransition(
        opacity: _opacityAnim,
        child: ScaleTransition(
          scale: _animScale,
          child: Center(
            child: CircularProgressIndicator.adaptive(),
          ),
        ),
      ),
    );
  }
}
