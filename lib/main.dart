import 'package:base_app/core/config/app_config.dart';
import 'package:base_app/core/di/injection.dart';
import 'package:base_app/core/enums/app_enum.dart';
import 'package:base_app/core/local_storage/local_storage.dart';
import 'package:base_app/core/network/dio_client.dart';
import 'package:base_app/core/config/flavor_config.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localization/flutter_localization.dart';

import 'app.dart';

Future<void> mainCommon(Environment env) async {
  WidgetsFlutterBinding.ensureInitialized();
  await FlavorConfig.initial(env);
  await FlutterLocalization.instance.ensureInitialized();
  await LocalStorage.instant.init();
  await configureDependencies();
  await AppConfig.initialConfig();

  getIt<DioClient>().init();
  runApp(const App());
}
