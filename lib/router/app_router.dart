import 'package:base_app/core/utils/navigation_util.dart';
import 'package:base_app/core/widgets/app_base.dart';
import 'package:base_app/core/widgets/app_error.dart';
import 'package:base_app/core/widgets/app_map.dart';
import 'package:base_app/features/activity/pages/activity_page.dart';
import 'package:base_app/features/activity/pages/detail_page.dart';
import 'package:base_app/features/home/<USER>/build_book_trip_widget.dart';
import 'package:base_app/features/login/page/login_page.dart';
import 'package:base_app/features/main/pages/main_page.dart';
import 'package:base_app/features/otp/pages/otp_page.dart';
import 'package:base_app/features/signup/pages/signup_page.dart';
import 'package:base_app/features/splash/splash_page.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class AppRouter {
  static const String splash = '/';
  static const String login = '/login';
  static const String error = '/error';
  static const String main = '/main';
  static const String activity = '/activity';
  static const String detail = '/detail';
  static const String map = '/map';
  static const String otp = '/otp';
  static const String signup = '/signup';
  static const String bookTrip = '/book-trip';

  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();

  static final GoRouter router = GoRouter(
    navigatorKey: navigatorKey,
    initialLocation: splash,
    routes: [
      GoRoute(
        path: splash,
        builder: (context, state) => const SplashPage(),
      ),
      GoRoute(
        path: login,
        pageBuilder: (context, state) {
          return NavigationUtil.withFade(
            key: state.pageKey,
            page: const LoginPage(),
          );
        },
      ),
      GoRoute(
        path: main,
        pageBuilder: (context, state) {
          return NavigationUtil.withFade(
            key: state.pageKey,
            page: const MainPage(),
          );
        },
      ),
      GoRoute(
        path: error,
        pageBuilder: (context, state) {
          return NavigationUtil.withFade(
            key: state.pageKey,
            page: const AppError(),
          );
        },
      ),
      GoRoute(
        path: activity,
        pageBuilder: (context, state) {
          return NavigationUtil.withFade(
            key: state.pageKey,
            page: const ActivityPage(),
          );
        },
      ),
      GoRoute(
        path: detail,
        pageBuilder: (context, state) {
          return NavigationUtil.withFade(
            key: state.pageKey,
            page: const DetailPage(),
          );
        },
      ),
      GoRoute(
        path: map,
        pageBuilder: (context, state) {
          return NavigationUtil.withFade(
            key: state.pageKey,
            page: AppMapbox(),
          );
        },
      ),
      GoRoute(
        path: otp,
        pageBuilder: (context, state) {
          final param = state.extra as Map<String, dynamic>;
          final verificationId = param['verificationId'];
          final resendToken = param['resendToken'];
          final userName = param['userName'];
          return NavigationUtil.withFade(
            key: state.pageKey,
            page: OtpPage(
              verificationId: verificationId,
              resendToken: resendToken,
              userName: userName,
            ),
          );
        },
      ),
      GoRoute(
        path: signup,
        pageBuilder: (context, state) {
          final param = state.extra as Map<String, dynamic>;
          final userName = param['userName'];
          final otpKey = param['otpKey'];
          return NavigationUtil.withFade(
            key: state.pageKey,
            page: SignupPage(
              userName: userName,
              otpKey: otpKey,
            ),
          );
        },
      ),
      GoRoute(
        path: bookTrip,
        pageBuilder: (context, state) {
          return NavigationUtil.withFade(
            key: state.pageKey,
            page: const BuildBookTripWidget(),
          );
        },
      ),
    ],
    errorBuilder: (context, state) => AppBase(
      body: Center(
        child: Text(
          'Page not found: ${state.uri}',
        ),
      ),
    ),
  );
}
